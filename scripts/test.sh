#!/usr/bin/env bash

# 创建 testdata 目录
mkdir -p testdata

# 测试时，如果go版本大于等图1.23, 那么需要增加checklinkname=0
go version | grep -q "go1.2[3-9]" && GO_BUILD_FLAGS="-ldflags=-checklinkname=0" || GO_BUILD_FLAGS=""
echo "GO_BUILD_FLAGS: $GO_BUILD_FLAGS"

# 使用 go list 命令获取所有包，然后排除 gen 和 example 目录
# 运行测试并生成覆盖率文件、详细的测试文件
xgo test -v -gcflags="all=-N -l" $GO_BUILD_FLAGS -cover -coverprofile=testdata/coverage.out -json $(go list ./... |grep -v -E "/(gen|example|.*\.pb)") > testdata/ut_detail.json

EXIT_CODE=$?

# 提取ut_detail.json中失败的测试用例
jq -r 'select(.Action=="fail" and .Test != null) | .Package + " " + .Test' testdata/ut_detail.json > testdata/ut_failed_tests.txt

if [ -s testdata/ut_failed_tests.txt ]; then
    echo "warning: 存在失败的测试用例, 请优化测试用例..."
    echo "===失败的测试用例==="
    cat testdata/ut_failed_tests.txt
    EXIT_CODE=100
fi

# 获取当前单用例耗时前10的测试用例

jq -r '                 
  select(.Action == "pass" or .Action == "fail")
  | select(.Test != null)
  | [.Package, .Test, .Elapsed] | @tsv
' testdata/ut_detail.json | sort -k 3 -h -r | head -n 10 > testdata/ut_case_cost_top10_tests.txt

# 获取当前测试耗时前10的package

jq -r '                 
  select(.Action == "pass" or .Action == "fail")
  | select(.Test == null)
  | [.Package, .Test, .Elapsed] | @tsv
' testdata/ut_detail.json | sort -k 2 -h -r | head -n 10 > testdata/ut_package_cost_top10_tests.txt

# 从testdata/ut_case_cost_top10_tests.txt文件获取用例耗时前1的用例, 如果耗时超过10s, 则报错

top1_case_cost=$(cat testdata/ut_case_cost_top10_tests.txt | head -n 1 | awk '{print $3}') || true 

if [ -n "$top1_case_cost" ]; then
    if awk "BEGIN {exit !($top1_case_cost > 10)}"; then
        echo "warning: 存在单用例耗时超过10s的用例, 请优化用例耗时..."
        echo "===用例耗时前10的用例==="
        cat testdata/ut_case_cost_top10_tests.txt
        EXIT_CODE=100
    fi
fi

top1_package_cost=$(cat testdata/ut_package_cost_top10_tests.txt | head -n 1 | awk '{print $2}') || true 

if [ -n "$top1_package_cost" ]; then
    if awk "BEGIN {exit !($top1_package_cost > 60)}"; then
        echo "warning: 存在单package耗时超过60s的package, 请优化package耗时..."
        echo "===package耗时前10的package==="
        cat testdata/ut_package_cost_top10_tests.txt
        EXIT_CODE=101
    fi
fi

# 提取当前测试中build failed的测试用例

jq -r 'select(.Action == "output" and (.Output | type == "string") and (.Output | test("build failed|undefined:|cannot find|import cycle"))) | .Package' testdata/ut_detail.json | sort -u > testdata/ut_build_failed_tests.txt

# 检查测试用例中是否存在build failed的测试用例

if [ -s testdata/ut_build_failed_tests.txt ]; then
    echo "warning: 存在build failed的测试用例, 请优化测试用例..."
    echo "===build failed的测试用例==="
    cat testdata/ut_build_failed_tests.txt
    EXIT_CODE=102
fi

# 输出覆盖率信息
if [ -f testdata/coverage.out ]; then
    total_coverage=$(go tool cover -func=testdata/coverage.out | tail -1 | awk '{print $NF}')
    echo "===测试覆盖率: $total_coverage==="
    # 生成可阅读的html报告
    go tool cover -html=testdata/coverage.out -o testdata/coverage.html
fi

echo "exec test finish, exit code: $EXIT_CODE"

exit $EXIT_CODE
