#!/bin/bash

# Exit immediately if a command exits with a non-zero status
set -e

git submodule update --remote

# Generate git hook scripts
HOOKS_DIR=.git/hooks
# Check if we are in a worktree
if [ -f .git ]; then
  # Read the actual git directory path
  GIT_DIR=$(cat .git | sed 's/gitdir: //')
  # Get the main repository .git directory
  HOOKS_DIR=$(dirname $(dirname $GIT_DIR))/hooks
fi
cp -a ./.ci-scripts/scripts/git-hooks/* "$HOOKS_DIR/"

# Init complete
echo "Project initialization completed..."
echo "If you need to install project dependencies, you can run 'make install-dep'"