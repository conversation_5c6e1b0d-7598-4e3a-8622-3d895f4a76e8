#!/usr/bin/env bash
set -e

FAST_MODE=${1:-false}
FULL_LINT=${2:-false}

echo "using params: fast_mode: ${FAST_MODE}, full_lint: ${FULL_LINT}"

EXTRA_ARGS=""

if [ "$FAST_MODE" = "true" ]; then
    EXTRA_ARGS="--fast-only"
fi

# 非全量模式, 这里需要获取获取公共祖先, 并使用--new-from-rev参数
if [ "$FULL_LINT" = "false" ]; then
    # 获取对比的目标分支
    branch_name=$(git ls-remote --exit-code --heads origin release >/dev/null 2>&1 && echo origin/release || echo origin/master)

    # 提取公共祖先
    common_ancestor=$(git merge-base $branch_name HEAD)

    # 获取此次新增/修改的文件名, 如果没有go文件, 则不进行lint
    go_files=$(git diff --name-status ${common_ancestor} HEAD | grep "^[AM]" |grep "\.go" |grep -v "_test\.go") || true 

    if [ -z "$go_files" ]; then
        echo "no go files change in this branch, skip lint"
        exit 0
    fi
    EXTRA_ARGS="$EXTRA_ARGS --new-from-rev=$common_ancestor"
fi 

echo "using extra args: $EXTRA_ARGS"

# 仅处理此次变更的代码
golangci-lint run --tests=false $EXTRA_ARGS --path-mode=abs --config=./.ci-scripts/rules/.golangci.yml 
