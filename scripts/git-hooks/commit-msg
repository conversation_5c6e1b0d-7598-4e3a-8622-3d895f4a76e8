#!/bin/bash

set -e 

COMMIT_MSG_FILE=$1

# 定义JIRA ticket格式正则表达式
JIRA_TICKET_PATTERN="[A-Za-z]{3,}-[0-9]{4,}"
BRANCH_HASH_PATTERN="BRANCH_HASH:[a-zA-Z0-9]{8}"

# 获取当前分支名和生成分支标识
current_branch=$(git branch --show-current)
branch_hash=$(echo "$current_branch" | md5sum | cut -c1-8)

# 读取当前的commit message
current_commit_msg=$(cat "$COMMIT_MSG_FILE")

# 跳过merge commit
if [[ "$current_commit_msg" == Merge\ * ]]; then 
    exit 0
fi

# 从指定的commit msg提取jira ticket
function extract_jira_ticket_from_commit_msg() {
    local commit_msg="$1"
    jira_ticket=""
    if [[ "$commit_msg" =~ $JIRA_TICKET_PATTERN ]]; then
        jira_ticket="${BASH_REMATCH[0]}"
    fi
    echo "$jira_ticket"
}

# 从commit history中提取jira ticket
function extract_jira_ticket_from_commit_history() {
    # 优先从之前的commit中查找jira ticket
    jira_ticket=""
    if git rev-parse --verify HEAD >/dev/null 2>&1; then
        # 使用分隔符来分隔不同的commit message
        # 跳过最新的commit，避免在amend时重复使用当前commit中可能错误的ticket
        while IFS= read -r -d '' commit_msg; do
            # 检查该commit message是否包含当前分支的branch hash
            if [[ "$commit_msg" == *"BRANCH_HASH:$branch_hash"* ]]; then
                # 在整个commit message中查找jira ticket
                potential_ticket=$(extract_jira_ticket_from_commit_msg "$commit_msg")
                if [[ -n "$potential_ticket" ]]; then
                    jira_ticket="$potential_ticket"
                    break
                fi
            fi
        done < <(git log --no-merges --skip=0 --pretty=format:"%B%x00" -n 10)
    fi
    echo "$jira_ticket"
}

# 从分支名提取jira ticket
function extract_jira_ticket_from_branch() {
    jira_ticket=""
    if [[ $(echo "$current_branch" | tr -cd '/' | wc -c) -eq 3 ]]; then
        branch_ticket=$(echo "$current_branch" | cut -d'/' -f3)
        if [[ "$branch_ticket" =~ $JIRA_TICKET_PATTERN ]]; then
            jira_ticket="$branch_ticket"
        fi
    fi
    echo "$jira_ticket"
}

function extract_jira_ticket() {
    jira_ticket=$(extract_jira_ticket_from_commit_history) 
    if [[ -z "$jira_ticket" ]]; then
        jira_ticket=$(extract_jira_ticket_from_branch)
    fi
    echo "$jira_ticket"
}

add_jira_ticket=false 

# 如果commit message中已存在jira-ticket格式，则不进行改写
if [[ ! $(echo "$current_commit_msg" | grep -v "#") =~ $JIRA_TICKET_PATTERN ]]; then
    add_jira_ticket=true 
fi

add_branch_hash=false
if [[ ! "$current_commit_msg" =~ $BRANCH_HASH_PATTERN ]]; then
    add_branch_hash=true
fi

if [[ "$add_jira_ticket" == "true" ]]; then
    jira_ticket=$(extract_jira_ticket)
    if [[ -n "$jira_ticket" ]]; then # 如果提取到jira ticket, 则添加到commit message, 否则忽略
        jira_ticket_upper=$(echo "$jira_ticket" | tr '[:lower:]' '[:upper:]')
        current_commit_msg="[$jira_ticket_upper] $current_commit_msg"
    fi
fi

if [[ "$add_branch_hash" == "true" ]]; then
    current_commit_msg="$current_commit_msg\n\nBRANCH_HASH:$branch_hash"
fi

# 将新的commit message写回文件
echo -e "$current_commit_msg" > "$COMMIT_MSG_FILE"
