#!/usr/bin/env bash
# check format
function validateJson() {
    cat $1 | json_pp > /dev/null
    if [ $? -ne 0 ];then
        echo "checking $1 error!!!"
        exit 1
    fi
#    echo "checking $1 passed"
}

function checkFilename() {
    fullname=$( echo $1)
    filename=${fullname%.*}
    if [[ ! $filename =~ ^[a-zA-Z]+\-[0-9]+\-[0-9]+$ ]]; then
      echo "checking filename $2 format invalid. use {name}-{service_id}-{rule_id}"
      exit 1
    fi
#    echo "checking filename $1 passed"
}

function iterateDir() {
    for file in `ls $1` ; do
    if [ -f "$1/$file" ]
    then
      checkFilename "$file" "$1/$file"
      validateJson "$1/$file"
    else
      iterateDir "$1/$file"
    fi
done
}

iterateDir rule
iterateDir example
echo "checking passed"
