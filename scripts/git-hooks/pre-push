#!/bin/bash

# Exit on error and undefined variables
set -e
set -u

# Global variables for branch and commit validation
EMAIL_REGEX='[a-zA-Z0-9._-]+'
TYPE_REGEX='(feature|hotfix|bugfix|doc|tech)'
JIRA_REGEX='[a-zA-Z]+-[0-9]+'
DESC_REGEX='[a-zA-Z0-9_-]+'
BRANCH_PATTERN="^${EMAIL_REGEX}/${TYPE_REGEX}/${JIRA_REGEX}/${DESC_REGEX}$"
VALID_COMMIT_PREFIXES="feature|hotfix|bugfix|chore|refactor|docs|test|ci|perf|style|fix|feat|doc"
COMMIT_FORMAT_PATTERN="^\[$JIRA_REGEX\][[:space:]]*\(?[[:space:]]*($VALID_COMMIT_PREFIXES)[[:space:]]*\)?[[:space:]]*:[[:space:]]*.{5,}$"

# Function to check branch name format
check_branch_name() {
    echo "Do branch name check..."
    
    # Get current branch name
    local BRANCH_NAME=$(git symbolic-ref --short HEAD)
    
    # 对于个人分支，规则为 <email-prefix>/<type>/<JIRA-ticket-number>/<description>
    # 示例：san.zhang/feautre/SPVG-1192/split-by-market
    # 仅使用这种4段规则, 其他规则均为不合法
    
    local valid=false
    
    if [[ $BRANCH_NAME =~ $BRANCH_PATTERN ]]; then
        valid=true
    fi

    if ! $valid; then 
        echo "✗ Found invalid branch name: ${BRANCH_NAME}"
        echo "Branch name must follow one of these patterns:"
        echo "Example:"
        echo "  - <email-prefix>/<type>/<JIRA-ticket-number>/<description>"
        echo "To bypass this check (not recommended), use"
        echo "  git push --no-verify"
        exit 1
    fi 

    echo "✓ Branch name check passed, current branch: ${BRANCH_NAME}"
}

# Function to check commit message format
check_commit_message() {
    echo "Do commit message check..."

    # 获取当前分支的所有 commit message
    # 只获取最新的2个commit，并且过滤掉包含"Merge branch"的提交
    # 下面是一些正确的commit例子:
    # 1. [JIRA-ISSUE-ID] feat: detailed changes
    # 2. [JIRA-ISSUE-ID] (feat): detailed changes

    local COMMITS=$(git log -n 2 --pretty=format:'%s' | grep -v "Merge ") || true  # 如果没有任何commit，则不报错

    if [ -z "$COMMITS" ]; then
        echo "✓ No commits to validate"
        return 0
    fi
    
    local INVALID_COMMITS=0
    
    while IFS= read -r COMMIT_MSG; do
        # 跳过空行
        if [ -z "$COMMIT_MSG" ]; then
            continue
        fi
        
        # 跳过包含"Merge branch"的提交
        if [[ "$COMMIT_MSG" == *"Merge "* ]]; then
            echo -e "Skipping merge commit: $COMMIT_MSG"
            continue
        fi
        
        if [[ "$COMMIT_MSG" =~ $COMMIT_FORMAT_PATTERN ]]; then
            # 不再显示有效提交消息
            continue
        else
            echo -e "✗ Found invalid commit message: $COMMIT_MSG"
            INVALID_COMMITS=$((INVALID_COMMITS + 1))
        fi
    done <<< "$COMMITS"
    
    if [ $INVALID_COMMITS -gt 0 ]; then
        echo -e "Commit message must follow the format:"
        echo -e "  [JIRA-ISSUE-ID] prefix: detailed changes"
        echo -e "Where:"
        echo -e "  - JIRA-ISSUE-ID is in format PROJ-123"
        echo -e "  - prefix is one of: ${VALID_COMMIT_PREFIXES}"
        echo -e "  - detailed changes should be at least 5 characters long"
        echo -e "Example: [SPVG-5061] feature: remove the duplicate unmarshal logic"
        echo -e "To bypass this check (not recommended), use:"
        echo -e "  git push --no-verify"
        exit 1
    fi 

    echo -e "✓ Commit message check passed"
}

# Function to run lint check
run_lint_check() {
    echo "Do linting check..."
    
    make lint fast-mode=true full-lint=false
    
    echo "✓ Linting check passed..."
}

# Main execution flow
main() {
    check_branch_name
    check_commit_message  
    run_lint_check
    
    echo "✓ All pre-push checks passed successfully!"
}

# Execute main function
main "$@"