#!/bin/bash

# Install necessary dependencies
go install github.com/golangci/golangci-lint/v2/cmd/golangci-lint@v2.1.6
go install github.com/google/wire/cmd/wire@latest
go install golang.org/x/tools/cmd/goimports@latest
go install google.golang.org/protobuf/cmd/protoc-gen-go@v1.27.1
go install git.garena.com/shopee/platform/spidl/cmd/protoc-gen-spidl@v1.0.0
go install github.com/gogo/protobuf/protoc-gen-gofast@v1.3.2
go install git.garena.com/shopee/common/spkit/cmd/protoc-gen-gogo-spex-rpc@latest
go install git.garena.com/shopee/common/spex-contrib/protoc-gen-spex-rpc@v0.1.0
go install git.garena.com/shopee/video/core/tools/clistub@latest
go install github.com/cweill/gotests/gotests@latest
go install github.com/xhd2015/xgo/cmd/xgo@v1.1.10