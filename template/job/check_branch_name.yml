check_branch_name:
  stage: pre_build
  tags:
    - video-core-runner
  script:
    - |
      if [[ "$CI_COMMIT_REF_NAME" =~ (master|release) ]]; then
        echo "主分支或发布分支, 跳过检查"
        exit 0
      fi

      IFS='/' read -ra BRANCH_PARTS <<< "$CI_COMMIT_REF_NAME"

      if [ ${#BRANCH_PARTS[@]} -ne 4 ]; then
        echo "错误: 分支名必须包含4个部分, 用'/'分隔: <email>/<type>/<jiraticket>/<desc>"
        echo "当前分支名: $CI_COMMIT_REF_NAME"
        exit 1
      fi

      EMAIL=${BRANCH_PARTS[0]}
      TYPE=${BRANCH_PARTS[1]}
      JIRATICKET=${BRANCH_PARTS[2]}
      DESC=${BRANCH_PARTS[3]}

      echo "分支名验证: $EMAIL/$TYPE/$JIRATICKET/$DESC"

      if ! echo "$JIRATICKET" | grep -Eq '^[A-Za-z]+-[0-9]+$'; then
        echo "错误: JIRA单号格式不正确"
        echo "JIRA单号应该是字母-数字的格式 (例如: ABC-123)"
        echo "当前JIRA单号: $JIRATICKET"
        exit 1
      fi

      echo "分支名验证通过..."
  allow_failure: false
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: always
