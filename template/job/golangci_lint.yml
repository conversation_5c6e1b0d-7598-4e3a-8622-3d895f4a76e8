golangci_lint:
  variables: 
    REVIEWDOG_CONFIG: |
      runner:
        golangci-lint:
          cmd: golangci-lint run --tests=false --path-mode=abs --build-tags=-buildvcs=false --config=.ci-scripts/rules/.golangci.yml --new-from-rev=$(git merge-base $(git ls-remote --exit-code --heads origin release >/dev/null 2>&1 && echo origin/release || echo origin/master) HEAD) && echo "success" > ./golangci_result.txt || echo "failed" > ./golangci_result.txt
          errorformat:
            - "%f:%l:%c: %m"
  stage: lint
  needs:
    - job: pre_build
      artifacts: true
    - job: build
  allow_failure: false
  before_script:
    - rm .ci-scripts .cursor -rf || true
    - git submodule deinit -f --all || true
    - git submodule update --init --recursive
    - git fetch origin $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME
    - git fetch origin $CI_MERGE_REQUEST_TARGET_BRANCH_NAME
  tags:
    - video-core-runner
  script:
    - echo "=== current ci rules ==="
    - cat .ci-scripts/rules/.golangci.yml
    - echo "=== current reviewdog rules ==="
    - echo "$REVIEWDOG_CONFIG" > ./.reviewdog.yml 
    - cat ./.reviewdog.yml
    - reviewdog -reporter=gitlab-mr-discussion -tee
    - echo "=== checking final result ==="
    - GOLANGCI_RESULT=$(cat ./golangci_result.txt)
    - if [ "$GOLANGCI_RESULT" = "failed" ]; then echo "golangci-lint failed, setting CI to failed"; exit 1; fi
    - echo "golangci-lint succeeded, CI passed"
  only:
    - merge_requests
