check_go_mod:
  stage: pre_build
  needs: [ pre_build ]
  script:
    - |
      go mod tidy -e || true
      changed_files=$(git status | grep modified | wc -l) || true
      if [ "$changed_files" -gt 0 ]; then
        echo "go.mod 或 go.sum 文件有修改, 请将修改后的文件提交到git.(执行一下 go mod tidy)"
        exit 10
      fi
      echo "go.mod/go.sum 检查通过..."
      exit 0
  tags:
    - video-core-runner
  allow_failure: false
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: always