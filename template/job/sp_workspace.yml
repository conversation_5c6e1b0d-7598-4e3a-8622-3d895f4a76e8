check_sp_workspace:
  stage: pre_build
  script:
    - if [ ! -f sp-workspace.yml ]; then echo "no sp-workspace.yml found, skip check"; exit 0; fi
    - error_lines=$(grep -E 'topic_name:' sp-workspace.yml | grep -v 'master' || true)
    - if [ -n "$error_lines" ]; then echo "found non-master topic_name, please fix it."; echo "invalid topic_name list:"; echo "$error_lines"; exit 2; fi
    - echo "all topic_name are master."
  tags:
    - video-core-runner
  allow_failure: false
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"