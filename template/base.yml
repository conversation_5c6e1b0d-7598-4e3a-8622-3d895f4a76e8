image: "harbor.shopeemobile.com/luckyvideo/golang-ci:latest"

include:
  - local: "/template/job/golangci_lint.yml"
  - local: "/template/job/nilaway.yml"
  - local: "/template/job/unittest.yml"
  - local: "/template/job/auto_merge.yml"
  - local: "/template/job/deploy.yml"
  - local: "/template/job/pre_build.yml"
  - local: "/template/job/sp_workspace.yml"
  - local: "/template/job/before_run.yml"
  - local: "/template/job/check_branch_name.yml"

stages:
  - pre_build
  - build
  - lint
  - test
  - deploy
  - auto_merge

variables:
  GOPRIVATE: "git.garena.com"

build:
  stage: build
  needs:
    - job: pre_build
      artifacts: true
  script:
    - make build
  tags:
    - video-core-runner
  allow_failure: false
  only:
    - merge_requests
