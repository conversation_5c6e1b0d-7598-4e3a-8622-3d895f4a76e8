## doc: https://golangci-lint.run/usage/configuration/
version: "2"

linters:
  default: standard
  enable:
    - staticcheck
    - gocyclo
    - lll
    - nestif
    - dupl
    - funlen
  disable:
    - unused
  settings:
    staticcheck:
      # SA1019 - Using a deprecated package, function, variable, constant or field
      checks: ["all", "-SA1019", "-ST1000", "-ST1003", "-ST1016", "-ST1020", "-ST1021", "-ST1022"]
    gocyclo:
      min-complexity: 20
    lll:
      line-length: 180
    nestif:
      min-complexity: 20
    dupl:
      threshold: 250
    funlen:
      lines: 512
      statements: -1
      ignore-comments: true
  exclusions:
    rules:
      - path: ".*_(test|example|mock)\\.go"
        linters:
          - staticcheck
          - ineffassign
          - govet
          - errcheck

issues:
  max-issues-per-linter: 0
  max-same-issues: 0
