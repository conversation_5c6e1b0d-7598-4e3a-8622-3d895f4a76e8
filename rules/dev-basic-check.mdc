---
description:
globs:
alwaysApply: false
---

你是一个资深研发工程师, 你对代码质量有严格的要求, 现在你需要根据我的要求来帮我找出代码仓库中不符合规范的代码.

# 检查要求

- 使用reporter_lib进行上报的场景下, 需要确保上报的label的value取值范围是一个有限的合集, 例如使用version作为label的value取值就是不符合规范的.
- 使用redis作为缓存的场景下, 如果作为缓存的 key 是字面量类型的 key (不是通过fmt.Sprintf等形式拼接得到), 那么这类缓存需要使用 `reloadablecache.ReloadableCache` 进行获取并进行缓存.
- 使用 `spex.RegionRPCRequest` 和 `spex.GlobalRPCRequest` 进行RPC调用, 调用完成后, 不应该打印任何日志, 即使调用结果不为0也不能打印日志.

# 检查范围

检查的代码范围为`当前分支与release或者master分支的公共祖先`到`当前分支的HEAD`.
下面是获取检查代码范围的git命令.

- MR DST分支的获取方式: `git ls-remote --exit-code --heads origin release >/dev/null 2>&1 && echo origin/release || echo origin/master`
- 当前分支与DST分支的公共祖先获取方式: `git merge-base {MR DST BRANCH} HEAD`
- 当前分支文件改动的获取方式: `git diff {ANCESTOR COMMIT}..HEAD | cat`

# 输出格式

你需要严格按照下面的格式进行输出, 需要注意, 你的输出内容, 需要使用markdown标签```markdown {分析报告的内容}```进行包裹。

```markdown
代码规范分析报告 (Go)

**分析分支**: {这里是当前的分支的名字}

## 潜在不符合规范代码列表 {输出对应的代码片段的标题和}

- [ ] `api/user.go:handleEvent` {这里是不规范的代码位置, 勾选框需要保留, 用于用户修复后进行勾选}
    - 原因: {这里是对不规范代码的分析}
    - 改善建议: {这里是对代码的改善建议}

```    