module git.garena.com/shopee/video/core/backend/bff_activity

go 1.21

require (
	git.garena.com/shopee/mts/go-application-server/gas v1.2.0
	git.garena.com/shopee/platform/golang_splib v1.2.12
	git.garena.com/shopee/platform/splog v1.8.0
	git.garena.com/shopee/platform/tracing v1.11.10
	git.garena.com/shopee/sp_protocol v1.3.35
	git.garena.com/shopee/video/core/backend/common_biz v0.5.0
	git.garena.com/shopee/video/core/common v1.2.0-beta.18
	github.com/golang/protobuf v1.5.4
	github.com/google/wire v0.5.0
	github.com/spf13/cast v1.4.1
	google.golang.org/protobuf v1.33.0
)

require (
	git.garena.com/common/gommon/crypt v0.0.0-20210211075301-867bb6bc3c33 // indirect
	git.garena.com/shopee/anti-fraud/kms-sdk/v2 v2.5.4-beta // indirect
	git.garena.com/shopee/common/circuitbreaker v0.8.0-rc.1 // indirect
	git.garena.com/shopee/common/gdbc/datum v0.1.1 // indirect
	git.garena.com/shopee/common/gdbc/hardy v0.11.0 // indirect
	git.garena.com/shopee/common/gdbc/parser v0.10.1 // indirect
	git.garena.com/shopee/common/gdbc/sddl v0.8.1 // indirect
	git.garena.com/shopee/common/jsonext v0.3.1 // indirect
	git.garena.com/shopee/common/log v0.8.0 // indirect
	git.garena.com/shopee/common/mq-contrib/muse v0.5.0 // indirect
	git.garena.com/shopee/common/mq-contrib/muse-protocol v0.1.2 // indirect
	git.garena.com/shopee/common/observability_config v0.2.1 // indirect
	git.garena.com/shopee/common/sarama v1.42.1-1.3 // indirect
	git.garena.com/shopee/common/ssoauth v0.12.0 // indirect
	git.garena.com/shopee/common/ssoauth_proto v0.5.0 // indirect
	git.garena.com/shopee/common/ulog v0.2.4 // indirect
	git.garena.com/shopee/core-server/enhanced-kafka-lib v0.14.0 // indirect
	git.garena.com/shopee/devops/golang_aegislib v0.0.14 // indirect
	git.garena.com/shopee/golang_splib v0.3.0 // indirect
	git.garena.com/shopee/mall-server/wsa-common/logger v1.6.0 // indirect
	git.garena.com/shopee/mall-server/wsa-common/logger/v2 v2.9.1 // indirect
	git.garena.com/shopee/mall-server/wsa-common/pool v1.0.0 // indirect
	git.garena.com/shopee/mall-server/wsa-common/serviceinfo v1.23.0 // indirect
	git.garena.com/shopee/mall-server/wsa-common/utils v1.49.0 // indirect
	git.garena.com/shopee/mts/sddl-api v0.2.0 // indirect
	git.garena.com/shopee/mts/servicecontext v0.2.0 // indirect
	git.garena.com/shopee/platform/config-sdk-go v0.10.0 // indirect
	git.garena.com/shopee/platform/ipds/ipds-sdk-go v0.4.1 // indirect
	git.garena.com/shopee/platform/local-tracer v1.2.0 // indirect
	git.garena.com/shopee/platform/service-governance/observability/metric v1.0.19 // indirect
	git.garena.com/shopee/platform/service-governance/viewercontext v1.0.13 // indirect
	git.garena.com/shopee/platform/trace v0.1.0 // indirect
	git.garena.com/shopee/platform/tracing-contrib/dynamic-sampler v0.1.1 // indirect
	git.garena.com/shopee/shopee-tracing/tracing-http v0.0.0-20210608015350-91ab1769246b // indirect
	git.garena.com/shopee/sz-devops/security-platform/kms/kms-common v1.0.12-0.20240904024514-d4d0b685118d // indirect
	git.garena.com/shopee/sz-devops/security-platform/kms/public_proto v1.3.2-0.20240904014427-b2f76bed9707 // indirect
	git.garena.com/shopee/video/core/fork_libs/aegis v0.2.7 // indirect
	git.garena.com/shopee/video/core/fork_libs/go-authsdk v1.2.1 // indirect
	git.garena.com/shopee/video/core/fork_libs/jaeger-tracer v1.8.1-sampler-option-patch3 // indirect
	git.garena.com/shopee/video/core/fork_libs/sqlparser v0.1.0-spv // indirect
	git.garena.com/shopee/video/core/sv-log-json v0.0.1 // indirect
	git.garena.com/shopee/video/infra/gctuner v0.1.3 // indirect
	github.com/DATA-DOG/go-sqlmock v1.5.0 // indirect
	github.com/Knetic/govaluate v3.0.1-0.20171022003610-9aa49832a739+incompatible // indirect
	github.com/Shopify/sarama v1.29.1 // indirect
	github.com/allegro/bigcache/v2 v2.2.5 // indirect
	github.com/allegro/bigcache/v3 v3.0.2 // indirect
	github.com/aws/aws-sdk-go v1.43.21 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/bytedance/go-tagexpr/v2 v2.9.3 // indirect
	github.com/bytedance/sonic v1.13.3 // indirect
	github.com/bytedance/sonic/loader v0.2.4 // indirect
	github.com/cespare/xxhash v1.1.0 // indirect
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/cloudwego/base64x v0.1.5 // indirect
	github.com/codegangsta/inject v0.0.0-20150114235600-33e0aa1cb7c0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/dgraph-io/ristretto v0.1.0 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/didi/gendry v1.7.0 // indirect
	github.com/dustin/go-humanize v1.0.0 // indirect
	github.com/eapache/go-resiliency v1.4.0 // indirect
	github.com/eapache/go-xerial-snappy v0.0.0-20230731223053-c322873962e3 // indirect
	github.com/eapache/queue v1.1.0 // indirect
	github.com/edsrzf/mmap-go v1.0.0 // indirect
	github.com/gabriel-vasile/mimetype v1.4.2 // indirect
	github.com/getsentry/sentry-go v0.10.0 // indirect
	github.com/gin-contrib/sse v0.1.0 // indirect
	github.com/gin-gonic/gin v1.9.1 // indirect
	github.com/go-ini/ini v1.44.0 // indirect
	github.com/go-ole/go-ole v1.2.6 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-playground/validator/v10 v10.14.0 // indirect
	github.com/go-redis/redis/v8 v8.11.5 // indirect
	github.com/go-resty/resty/v2 v2.7.0 // indirect
	github.com/go-sql-driver/mysql v1.7.0 // indirect
	github.com/go-yaml/yaml v2.1.0+incompatible // indirect
	github.com/goccy/go-json v0.10.2 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang/glog v1.1.0 // indirect
	github.com/golang/mock v1.6.0 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/google/go-cmp v0.6.0 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/grpc-ecosystem/grpc-gateway v1.16.0 // indirect
	github.com/hashicorp/errwrap v1.0.0 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/hashicorp/go-uuid v1.0.3 // indirect
	github.com/hashicorp/golang-lru v0.6.0 // indirect
	github.com/henrylee2cn/ameda v1.4.10 // indirect
	github.com/henrylee2cn/goutil v0.0.0-20210127050712-89660552f6f8 // indirect
	github.com/inconshreveable/mousetrap v1.0.0 // indirect
	github.com/jcmturner/aescts/v2 v2.0.0 // indirect
	github.com/jcmturner/dnsutils/v2 v2.0.0 // indirect
	github.com/jcmturner/gofork v1.7.6 // indirect
	github.com/jcmturner/gokrb5/v8 v8.4.4 // indirect
	github.com/jcmturner/rpc/v2 v2.0.3 // indirect
	github.com/jinzhu/copier v0.4.0 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/kelseyhightower/envconfig v1.4.0 // indirect
	github.com/klauspost/compress v1.17.9 // indirect
	github.com/klauspost/cpuid/v2 v2.2.4 // indirect
	github.com/leodido/go-urn v1.2.4 // indirect
	github.com/lufia/plan9stats v0.0.0-20230110061619-bbe2e5e100de // indirect
	github.com/mattn/go-isatty v0.0.19 // indirect
	github.com/matttproud/golang_protobuf_extensions/v2 v2.0.0 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/nyaruka/phonenumbers v1.0.55 // indirect
	github.com/opentracing/opentracing-go v1.2.0 // indirect
	github.com/panjf2000/ants/v2 v2.8.2 // indirect
	github.com/pbnjay/memory v0.0.0-20210728143218-7b4eea64cf58 // indirect
	github.com/pelletier/go-toml/v2 v2.0.8 // indirect
	github.com/pierrec/lz4 v2.6.1+incompatible // indirect
	github.com/pierrec/lz4/v4 v4.1.21 // indirect
	github.com/pingcap/errors v0.11.5-0.20210425183316-da1aaba5fb63 // indirect
	github.com/pingcap/log v0.0.0-20210625125904-98ed8e2eb1c7 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/power-devops/perfstat v0.0.0-20221212215047-62379fc7944b // indirect
	github.com/pquerna/ffjson v0.0.0-20190930134022-aa0246cd15f7 // indirect
	github.com/prometheus/client_golang v1.18.0 // indirect
	github.com/prometheus/client_model v0.5.0 // indirect
	github.com/prometheus/common v0.45.0 // indirect
	github.com/prometheus/procfs v0.12.0 // indirect
	github.com/rcrowley/go-metrics v0.0.0-20201227073835-cf1acfcdf475 // indirect
	github.com/rs/xid v1.3.0 // indirect
	github.com/shamaton/msgpack v1.1.1 // indirect
	github.com/shirou/gopsutil v2.21.11+incompatible // indirect
	github.com/shirou/gopsutil/v3 v3.23.12 // indirect
	github.com/shoenig/go-m1cpu v0.1.6 // indirect
	github.com/sony/gobreaker v1.0.0 // indirect
	github.com/spf13/cobra v0.0.5 // indirect
	github.com/spf13/pflag v1.0.3 // indirect
	github.com/stretchr/objx v0.5.0 // indirect
	github.com/stretchr/testify v1.8.4 // indirect
	github.com/tklauser/go-sysconf v0.3.12 // indirect
	github.com/tklauser/numcpus v0.6.1 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/uber/jaeger-client-go v2.22.1+incompatible // indirect
	github.com/uber/jaeger-lib v2.2.0+incompatible // indirect
	github.com/ugorji/go/codec v1.2.11 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasttemplate v1.2.1 // indirect
	github.com/vmihailenco/msgpack v4.0.4+incompatible // indirect
	github.com/xdg-go/pbkdf2 v1.0.0 // indirect
	github.com/xdg-go/scram v1.1.2 // indirect
	github.com/xdg-go/stringprep v1.0.4 // indirect
	github.com/xdg/scram v1.0.3 // indirect
	github.com/xdg/stringprep v1.0.3 // indirect
	github.com/yusufpapurcu/wmi v1.2.3 // indirect
	go.uber.org/atomic v1.9.0 // indirect
	go.uber.org/multierr v1.7.0 // indirect
	go.uber.org/zap v1.19.1 // indirect
	golang.org/x/arch v0.3.0 // indirect
	golang.org/x/crypto v0.14.0 // indirect
	golang.org/x/net v0.17.0 // indirect
	golang.org/x/sync v0.4.0 // indirect
	golang.org/x/sys v0.15.0 // indirect
	golang.org/x/text v0.13.0 // indirect
	golang.org/x/time v0.3.0 // indirect
	golang.org/x/xerrors v0.0.0-20220907171357-04be3eba64a2 // indirect
	google.golang.org/appengine v1.6.7 // indirect
	google.golang.org/genproto v0.0.0-20230530153820-e85fd2cbaebc // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20230530153820-e85fd2cbaebc // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20230530153820-e85fd2cbaebc // indirect
	google.golang.org/grpc v1.55.0 // indirect
	google.golang.org/grpc/cmd/protoc-gen-go-grpc v1.1.0 // indirect
	gopkg.in/natefinch/lumberjack.v2 v2.0.0 // indirect
	gopkg.in/resty.v1 v1.12.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	gorm.io/gorm v1.23.8 // indirect
	pgregory.net/rand v1.0.2 // indirect
)
