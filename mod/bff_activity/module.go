package module

import (
	"context"
	"log"

	"git.garena.com/shopee/mts/go-application-server/gas"
	"git.garena.com/shopee/platform/golang_splib/sps"
	"git.garena.com/shopee/video/core/backend/bff_activity/gen/bffutil"
	"git.garena.com/shopee/video/core/backend/bff_activity/internal"
	"git.garena.com/shopee/video/core/backend/bff_activity/internal/infra"
	"git.garena.com/shopee/video/core/backend/common_biz/tsp"
	"git.garena.com/shopee/video/core/common/bizpkg/video/xbootstarter"
	"git.garena.com/shopee/video/core/common/cid"
	"git.garena.com/shopee/video/core/common/cid/cidconfig"
	"git.garena.com/shopee/video/core/common/config"
	jobxboot "git.garena.com/shopee/video/core/common/jobextra/xboot"
	"git.garena.com/shopee/video/core/common/jobkit/jobstarter"
	"git.garena.com/shopee/video/core/common/logutil"
	mspex "git.garena.com/shopee/video/core/common/ratelimit/middleware/spex"
	"git.garena.com/shopee/video/core/common/spex"
	"git.garena.com/shopee/video/core/common/spex/bffinterceptor"
	"git.garena.com/shopee/video/core/common/spex/bffinterceptor/rewrites"
	"git.garena.com/shopee/video/core/common/xboot"
)

func serviceEntry() {
	rewriteSpexRegister()
	registry, delayChecker := infra.NewConfigRegistry()
	// initDependencies
	err := initDependencies()
	if err != nil {
		log.Fatalf("dependency init failed, err=%v", err)
	}
	server, err := internal.InitServer(registry)
	if err != nil {
		log.Fatal(err)
	}

	opts := []xboot.Option{
		xboot.WithDefaultStarterOption(),
		xboot.WithUseDefaultInterceptors(),
		xboot.WithAddStarterInitFunc(delayChecker),
		xboot.WithAddStarterInitFunc(enableMiddleware),
		xboot.WithAddStarterInitFunc(mspex.ApplyPriorityLimiterToSpexProcessorMiddleware),
		xboot.WithAddStarterInitFunc(func() {
			jobstarter.MustInitJobSdk(
				jobstarter.EnableLogResultHook(),
				jobstarter.EnableMetricHook(),
				jobstarter.EnableDefaultRPCStatisticHook(spex.RegionRPCRequest),
			)
		}, xboot.Cli),
	}
	opts = append(opts, xbootstarter.GenerateXbootOptions()...)

	xboot.NewFromAwareConfig(func() config.ContextAwareConfig {
		return registry.GetConfig("server_config").(config.ContextAwareConfig)
	}, cidconfig.GetDefaultServeCidInfo(),
		opts...).
		RegisterGlobalCmdServerInterceptor(jobxboot.ObserveProcessorRuntimeStatisticFn, xboot.Cli).
		Serves(server.Services).
		Run()
}

func rewriteSpexRegister() {
	register := spex.RegisterProcessorWithGlobal
	spex.RegisterProcessorWithGlobal = func(pc *sps.ProcessorConfig, opts ...spex.RewriteOpt) {
		register(pc, spex.WithEnableSecurityDetect())
	}
}

type dep struct {
	name string
	fn   func(bizName string) error
}

func initDependencies() error {
	deps := []dep{
		{"luckyvideo_redis", infra.InitRedis},
	}
	logger := logutil.GetLogger(cid.ContextForGlobalDaemon(context.Background()))
	for _, d := range deps {
		if err := d.fn(d.name); err != nil {
			logger.WithError(err).WithField("name", d.name).Error("init fail")
			return err
		}
		logger.WithField("name", d.name).Info("init succ")
	}
	return nil
}

func enableMiddleware() {
	tsp.MustInitManager()
	spex.ResetBffMiddlewareGroup(bffinterceptor.WithEnable(),
		bffinterceptor.WithRedis(bffinterceptor.WrapRedisClient(infra.RedisClient().GetClientContext)))
	rewrites.ResetErrMsgRewrite(bffutil.CodeToMsg)
}

func RegisterModule() (*gas.Module, error) {
	return gas.New(
		gas.Object(new(module)),
	)
}

type module struct {
}

func (m *module) Run(ctx context.Context, shutdown <-chan gas.ShutdownInfo) error {
	serviceEntry()
	return nil
}
