[{"id": 1, "target_type": 0, "cgi_type": 1, "prefix": "/forward", "path": "/like2/post", "remark": "uat 测试新增", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.follow.like_post?<cid>", "timeout": 1000, "category": "forward", "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 2, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/test_forward_example", "remark": "example for dev who want to write a forward cgi", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.retrieve.get_hashtag_detail?<cid>", "timeout": 800, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 3, "target_type": 1, "cgi_type": 1, "prefix": "/unisearch", "path": "/recommend_item_list", "remark": "from spex config: yihan.xue", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.unisearch.unisearchbff.recommend_item_list?<cid>_global", "timeout": 2000, "category": "forward", "need_metas": false, "meta_list": ["userid", "shopid", "country", "language", "deviceid", "rn_version"], "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 4, "target_type": 1, "cgi_type": 1, "prefix": "/unisearch", "path": "/query_item_list", "remark": "from spex config: yihan.xue", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.unisearch.unisearchbff.query_item_list?<cid>_global", "timeout": 2000, "category": "forward", "need_metas": false, "meta_list": ["userid", "shopid", "country", "language", "deviceid", "rn_version"], "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 5, "target_type": 1, "cgi_type": 1, "prefix": "/unisearch", "path": "/home_page", "remark": "from spex config: yihan.xue", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.unisearch.unisearchbff.home_page?<cid>_global", "timeout": 2000, "category": "forward", "need_metas": false, "meta_list": ["userid", "shopid", "country", "language", "deviceid", "rn_version"], "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 6, "target_type": 1, "cgi_type": 1, "prefix": "/unisearch", "path": "/search", "remark": "from spex config: yihan.xue", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.unisearch.unisearchbff.search?<cid>_global", "timeout": 2000, "category": "forward", "need_metas": false, "meta_list": ["userid", "shopid", "country", "language", "deviceid", "rn_version", "device_fingerprint"], "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 7, "target_type": 1, "cgi_type": 1, "prefix": "/unisearch", "path": "/splash_hint", "remark": "from spex config: yihan.xue", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.unisearch.unisearchbff.splash_hint?<cid>_global", "timeout": 2000, "category": "forward", "need_metas": false, "meta_list": ["userid", "shopid", "country", "language", "deviceid", "rn_version"], "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 8, "target_type": 1, "cgi_type": 1, "prefix": "/forward", "path": "/test_bff_forwarder", "remark": "from spex config: qian.zhang", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_sticker_list?<cid>_global", "timeout": 1000, "category": "forward", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["biz_type"]}], "ext_info": {}}, {"id": 9, "target_type": 0, "cgi_type": 1, "prefix": "/forward", "path": "/test_dynamic_forward_1", "remark": "from spex config", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST", "GET"], "target": "luckyvideo.retrieve.get_hashtag_detail?<cid>", "need_login": true, "timeout": 500, "category": "forward", "need_metas": true, "need_headers": false, "need_cookies": false, "int_params": ["user_id", "hashtag_id", "magic_engine"]}], "ext_info": {}}, {"id": 10, "target_type": 0, "cgi_type": 1, "prefix": "/resource", "path": "/music/karaoke/random", "remark": "11.11", "status": 2, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.retrieve.get_random_karaoke_music_list?<cid>", "timeout": 1000, "category": "forward", "need_queries": true, "need_metas": false, "meta_list": ["userid"], "need_headers": false, "need_cookies": false, "int_params": ["limit"]}], "ext_info": {}}, {"id": 11, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/resource/music/karaoke/random", "remark": "karaoke config for 11.11 game", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.retrieve.get_random_karaoke_music_list?<cid>", "timeout": 1000, "category": "read", "need_queries": true, "need_metas": false, "meta_list": ["userid"], "need_headers": false, "need_cookies": false, "int_params": ["limit", "magic_id"]}], "ext_info": {}}, {"id": 12, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/resource/sticker/list", "remark": "sticker", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_sticker_list_v2?<cid>", "timeout": 2000, "category": "read", "need_queries": false, "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["biz_type"]}], "ext_info": {}}, {"id": 13, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/resource/sticker/info", "remark": "sticker info", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_resource.get_sticker_infos_v2?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 14, "target_type": 0, "cgi_type": 1, "prefix": "/forward", "path": "/dzc/get_user_detail", "remark": "11", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET", "POST"], "target": "luckyvideo.user.get_user_details?<cid>", "timeout": 1000, "category": "forward", "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 15, "target_type": 0, "cgi_type": 1, "prefix": "/forward", "path": "/dzc/get_user_detail_1", "remark": "11", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.user.get_user_details?<cid>", "timeout": 1000, "category": "forward", "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 16, "target_type": 0, "cgi_type": 1, "prefix": "/forward", "path": "/dzc/get_user_detail_2", "remark": "111", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET", "POST"], "target": "luckyvideo.user.get_user_details?<cid>", "timeout": 1000, "category": "forward", "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 17, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/user/history_vv", "remark": "migrate from luckyvideo api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.retrieve.get_user_history_vv?<cid>", "need_login": true, "timeout": 1200, "category": "read", "need_metas": false, "meta_list": ["userid"], "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 18, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/banner", "remark": "migrate from luckyvideo.api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.retrieve.get_banners?<cid>", "timeout": 1200, "category": "read", "need_metas": false, "meta_list": ["userid", "device_fingerprint"], "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "ip"}, {"name": "user_id_filler", "param": {"field_name": "user_id"}}, {"name": "device_info_filler", "param": {"field_name": "device_info", "with_client_device_info": true}}]}}}, {"id": 19, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/shopee/url/validate", "remark": "migrate from luckyvideo.api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.retrieve.shopee_url_validate?<cid>", "need_login": true, "timeout": 1200, "category": "read", "need_metas": false, "meta_list": ["userid"], "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 20, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/reddot/info", "remark": "migrate from luckyvideo.api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.retrieve.get_reddot_info?<cid>", "timeout": 1200, "category": "read", "need_metas": false, "meta_list": ["userid"], "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 21, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/affiliate/item/list", "remark": "migrate from luckyvideo.api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.retrieve.get_items_with_affiliate?<cid>", "timeout": 1200, "category": "read", "need_metas": false, "meta_list": ["userid"], "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 22, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/share/send_msg", "remark": "migrate from luckyvideo.api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.biz.send_share_msg?<cid>", "need_login": true, "timeout": 2000, "category": "write", "need_metas": false, "meta_list": ["userid"], "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 23, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/survey/submit", "remark": "migrate from luckyvideo.api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.biz.submit_survey_result?<cid>", "timeout": 1200, "category": "write", "need_metas": false, "meta_list": ["userid"], "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 24, "target_type": 1, "cgi_type": 0, "prefix": "/", "path": "/video/item/list", "remark": "migrate from luckyvideo.api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.unisearch.unisearchbff.video_item_list?<cid>", "need_login": true, "timeout": 1200, "category": "read", "need_metas": false, "meta_list": ["userid", "shopid"], "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 25, "target_type": 1, "cgi_type": 0, "prefix": "/", "path": "/video/item/user_tabs", "remark": "migrate from luckyvideo.api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.unisearch.unisearchbff.video_item_usertabs?<cid>", "need_login": true, "timeout": 1200, "category": "read", "need_metas": false, "meta_list": ["userid", "shopid"], "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 26, "target_type": 1, "cgi_type": 0, "prefix": "/", "path": "/videolive/heartbeat", "remark": "migrate from luckyvideo.api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.bff.videolive.heartbeat?<cid>_global", "need_login": false, "timeout": 1200, "category": "read", "need_metas": false, "meta_list": ["userid", "shopid", "deviceid", "client_ip"], "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 27, "target_type": 1, "cgi_type": 0, "prefix": "/", "path": "/videolive/check", "remark": "migrate from luckyvideo.api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.bff.videolive.check?<cid>_global", "timeout": 1200, "category": "read", "need_metas": false, "meta_list": ["userid", "shopid", "client_ip"], "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 28, "target_type": 1, "cgi_type": 0, "prefix": "/", "path": "/videolive/info", "remark": "migrate from luckyvideo.api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.bff.videolive.info?<cid>_global", "timeout": 1200, "category": "read", "need_metas": false, "meta_list": ["userid", "shopid", "client_ip", "deviceid"], "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 29, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/resource/sticker/tab/data", "remark": "sticker分类下数据列表", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_sticker_tab_data?<cid>", "timeout": 1000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["tab_id", "limit"]}], "ext_info": {}}, {"id": 30, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/resource/sticker/tabs", "remark": "sticker分类列表", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_sticker_tabs?<cid>", "timeout": 1000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["scene_type"]}], "ext_info": {}}, {"id": 31, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/eoy/material", "remark": "eoy活动资源文件", "status": 2, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_eoy_material?<cid>", "need_login": false, "timeout": 1000, "category": "read", "need_queries": true, "need_metas": false, "meta_list": ["userid"], "need_headers": false, "need_cookies": false, "int_params": ["game_version", "user_version", "request_type"]}], "ext_info": {}}, {"id": 32, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/resource/template/card/list", "remark": "get template card list", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_template_card?<cid>", "timeout": 1000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["os_type", "android_performance"]}], "ext_info": {}}, {"id": 33, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/resource/template/badge/list", "remark": "get template badge list", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_template_badge?<cid>", "timeout": 1000, "category": "read", "need_queries": false, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 34, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/review/like", "remark": "/review/like", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST", "GET"], "target": "luckyvideo.retrieve.review_like?<cid>", "timeout": 5000, "category": "read", "need_metas": false, "meta_list": ["userid", "session_id", "client_ip", "forwarded_for", "user_agent", "sso_token", "url", "shopee_token", "country", "device_fingerprint", "app_type", "language", "app_version", "rn_version", "referer", "host", "real_ip", "tracking_session_id", "deviceid", "method", "rn_bundle_version", "client_id", "shopid", "is_from_xiapibuy"], "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 35, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/item/detail", "remark": "/item/detail", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.retrieve.get_item_detail?<cid>", "timeout": 5000, "category": "read", "need_metas": false, "meta_list": ["userid", "language", "device_fingerprint", "app_type", "app_version", "rn_version", "shopid", "url", "session_id", "forwarded_for", "client_ip"], "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 36, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/hashtag/detail", "remark": "test plugin", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_activity.get_hashtag_detail?<cid>", "timeout": 1000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["magic_engine"], "bool_params": ["template_with_tab"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "app_header", "remark": "app header data", "param": {"params": ["cache_level"]}}, {"name": "user_id_filler", "param": {"field_name": "user_id"}}, {"name": "device_info_filler", "param": {"field_name": "device_info", "with_client_device_info": true}}]}}}, {"id": 37, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/filter/get_filter_class_list", "remark": "get filter class list", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_resource.get_filter_class_list?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 38, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/filter/get_filter_detail", "remark": "get filter detail api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_resource.get_filter_detail?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 39, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/filter/get_filter_list", "remark": "get filter list api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_resource.get_filter_list?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 40, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/resource/template/tabs", "remark": "get template tabs", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_template_class_list?<cid>", "timeout": 1000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "device_info_filler", "param": {"field_name": "device_info"}}]}}}, {"id": 41, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/resource/magic/pop/all", "remark": "get magic all pop", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_all_pop_magic?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["magic_engine"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "ip"}, {"name": "user_id_filler", "param": {"field_name": "user_id"}}, {"name": "device_info_filler", "param": {"field_name": "device_info", "with_client_device_info": true}}]}}}, {"id": 42, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/music/classifications", "remark": "get music class api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_music_classifications?<cid>", "timeout": 1000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 43, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/post/control_info/list", "remark": "get post control api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_post_control_infos?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 44, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/resource/magic/tabs", "remark": "get magic tabs api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_magic_class_list?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["scene_type", "magic_engine"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "ip"}, {"name": "user_id_filler", "param": {"field_name": "user_id"}}, {"name": "device_info_filler", "param": {"field_name": "device_info", "with_client_device_info": true}}]}}}, {"id": 45, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/magic/online/list", "remark": "get magic online list api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_online_magic_display_infos?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["magic_engine"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "ip"}, {"name": "user_id_filler", "param": {"field_name": "user_id"}}, {"name": "device_info_filler", "param": {"field_name": "device_info", "with_client_device_info": true}}]}}}, {"id": 46, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/search/result/user", "remark": "get magic online list api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.retrieve.get_search_result_page_users?<cid>", "timeout": 1000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "message_conv"}]}}}, {"id": 47, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/appeal/result", "remark": "get appeal result api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_safety.get_appeal_result?<cid>", "timeout": 2000, "category": "read", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["object_type"]}], "ext_info": {}}, {"id": 48, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/resource/magic/tab/data", "remark": "get magic tab data api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_magic_list?<cid>", "timeout": 1000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["tab_id", "magic_engine"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "ip"}, {"name": "user_id_filler", "param": {"field_name": "user_id"}}, {"name": "device_info_filler", "param": {"field_name": "device_info", "with_client_device_info": true}}]}}}, {"id": 49, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/resource/thumbnail_text/list", "remark": "get thumbnail_text list api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_thumbnail_text_list?<cid>", "timeout": 1000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 50, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/friends/activity/list", "remark": "get friends activity list api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_activity.get_friend_activity_list?<cid>", "timeout": 2000, "category": "read", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["limit"], "bool_params": ["need_viewed_data"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 51, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/magic/page/detail", "remark": "get magic page detail api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_magic_page_detail?<cid>", "timeout": 1000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["magic_id", "magic_engine"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}, {"name": "device_info_filler", "param": {"field_name": "device_info", "with_client_device_info": true}}]}}}, {"id": 52, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/resource/magic/pop", "remark": "get magic pop api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_pop_magic?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["magic_engine"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "device_info_filler", "param": {"field_name": "device_info", "with_client_device_info": false}}]}}}, {"id": 53, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/user/live/info", "remark": "get user live info api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_user.batch_get_user_live_info?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "self_user_id"}}]}}}, {"id": 54, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/template/page/detail", "remark": "get template page detail api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_template_page_detail?<cid>", "timeout": 1000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["template_id"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}, {"name": "device_info_filler", "param": {"field_name": "device_info", "with_client_device_info": true}}]}}}, {"id": 55, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/resource/template/info", "remark": "get template info api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_template_info?<cid>", "timeout": 1000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["class_id", "template_id"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "device_info_filler", "param": {"field_name": "device_info"}}]}}}, {"id": 56, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/resource/effect/list", "remark": "get effect list api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_effect_list_v2?<cid>", "timeout": 1000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["tab_id"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "device_info_filler", "param": {"field_name": "device_info"}}]}}}, {"id": 57, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/resource_banner", "remark": "get resource_banner api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_resource_banners?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["banner_type", "magic_engine"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}, {"name": "device_info_filler", "param": {"field_name": "device_info", "with_client_device_info": true}}]}}}, {"id": 58, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/friends/activity/count", "remark": "get friends activity count api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_activity.get_friend_activity_count?<cid>", "timeout": 1000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["useRcmd"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 59, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/template/online/list", "remark": "get template online list api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_online_template_display_infos?<cid>", "timeout": 1000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "ip"}, {"name": "device_info_filler", "param": {"field_name": "device_info", "with_client_device_info": true}}]}}}, {"id": 60, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/resource/effect/tab/list", "remark": "get effect tab list api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_effect_class_list?<cid>", "timeout": 1000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}, {"name": "device_info_filler", "param": {"field_name": "device_info"}}]}}}, {"id": 61, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/hint/info", "remark": "get hit info api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_hint_infos?<cid>", "timeout": 1000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["magic_engine"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}, {"name": "device_info_filler", "param": {"field_name": "device_info", "with_client_device_info": true}}]}}}, {"id": 62, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/magic/detail", "remark": "get magic detail api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_magic_detail?<cid>", "timeout": 1000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["magic_id", "magic_engine"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}, {"name": "device_info_filler", "param": {"field_name": "device_info", "with_client_device_info": true}}]}}}, {"id": 63, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/appeal/account", "remark": "get appeal account api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_safety.save_account_appeal?<cid>", "need_login": true, "timeout": 2000, "category": "write", "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 64, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/resource/magic/info", "remark": "get magic info api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_resource.get_magic_info?<cid>", "timeout": 1000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "device_info_filler", "param": {"field_name": "device_info"}}]}}}, {"id": 65, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/resource/music/info", "remark": "get music info api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_resource.get_music_info?<cid>", "timeout": 1000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 66, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/resource/effect/info", "remark": "get effect info api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_resource.get_effect_info?<cid>", "timeout": 1000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "device_info_filler", "param": {"field_name": "device_info"}}]}}}, {"id": 67, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/follow/template/list", "remark": "get follow template list api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_activity.get_user_following_template_list?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["user_id", "limit"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 68, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/follow/music/list", "remark": "get follow music list api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_activity.get_user_following_music_list?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["user_id", "limit"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "owner_id"}}]}}}, {"id": 69, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/hashtag/recommend", "remark": "get hashtag recommend api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_activity.get_recommend_hashtag?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["limit"]}], "ext_info": {}}, {"id": 70, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/follow/magic/list", "remark": "get follow magic api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_activity.get_user_following_magic_list?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["user_id", "limit"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "owner_id"}}]}}}, {"id": 71, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/friends/post/share", "remark": "get friends post share api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.retrieve.get_post_share_of_friends?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "auth"}, {"name": "message_conv"}]}}}, {"id": 72, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/music/detail", "remark": "get music detail api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_music_detail?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}, {"name": "app_header", "remark": "app header data", "param": {"params": ["client_ip"]}}]}}}, {"id": 73, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/follow/list", "remark": "get follow list api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_activity.get_user_following_list?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["user_id", "limit"], "bool_params": ["need_following_music", "need_following_hashtag", "need_following_magic", "need_following_template"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "owner_id"}}, {"name": "app_header", "remark": "app header data", "param": {"params": ["client_ip"]}}]}}}, {"id": 74, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/follow/followers", "remark": "get follow followers api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_activity.get_user_follower_list?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["user_id", "limit"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "owner_id"}}, {"name": "app_header", "remark": "app header data", "param": {"params": ["client_ip"]}}]}}}, {"id": 75, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/follow/hashtag/list", "remark": "get follow hashtag list api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_activity.get_user_following_hashtag_list?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["user_id", "limit"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "owner_id"}}]}}}, {"id": 76, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/resource/template/list", "remark": "get template list api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_template_list_v2?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["class_id", "limit"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "auth"}, {"name": "device_info_filler", "param": {"field_name": "device_info"}}]}}}, {"id": 77, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/resource/template/tab/data", "remark": "get template tab data api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_template_list_v2?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["class_id", "limit"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "device_info_filler", "param": {"field_name": "device_info"}}, {"name": "default_values_filler", "param": {"values": [{"field_name": "with_tab", "type": "bool", "value": "true"}]}}]}}}, {"id": 78, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/timeline/me", "remark": "get me page timeline api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_timeline.get_me_page_timeline?<cid>", "timeout": 6000, "category": "read", "meta_list": ["userid", "language"], "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["request_user_id", "need_total_count", "limit"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "ip"}, {"name": "app_header", "remark": "app header data", "param": {"params": ["client_ip", "request_info", "geo_ip_country"]}}]}}}, {"id": 79, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/whitelist/check_if_in_list", "remark": "test check list", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.retrieve.check_if_in_tag_list?<cid>", "timeout": 1000, "category": "read", "need_login": true, "need_metas": false, "meta_list": ["userid"], "need_headers": false, "need_cookies": false}], "ext_info": {"rule_conv": {"enable": true, "input": [], "output": [{"type": "move", "path": "$.in_tag_list", "type_move": {"path": "$.", "name": "is_in_list"}}]}}}, {"id": 80, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/whitelist/opt_list", "remark": "test opt list", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.retrieve.opt_tag_list?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "meta_list": ["userid"], "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 81, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/comment/list", "remark": "get comment list api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_activity.get_comments?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["limit", "algo"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 82, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/comment/replies", "remark": "get comment replies api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_activity.get_replies?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["limit"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 83, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/post/products", "remark": "get post product api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET", "POST"], "target": "luckyvideo.core.bff_ecommerce.get_post_products?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}, {"name": "app_header", "remark": "app header data", "param": {"params": ["request_info", "geo_ip_country"]}}]}}}, {"id": 84, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/report/post", "remark": "report post api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_safety.report_post?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "reporter_id"}}]}}}, {"id": 85, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/report/user", "remark": "report user api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_safety.report_user?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "reporter_id"}}]}}}, {"id": 86, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/dislike/creator", "remark": "dislike creator api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_safety.dislike_creator?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 87, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/dislike/post", "remark": "dislike post api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_safety.dislike_post?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 88, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/appeal/post", "remark": "appeal post api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_safety.save_post_appeal?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 89, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/follow/user", "remark": "follow user api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_activity.follow_user?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}, {"methods": ["DELETE"], "target": "luckyvideo.core.bff_activity.unfollow_user?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "ip"}, {"name": "user_id_filler", "param": {"field_name": "from_user_id"}}, {"name": "client_device_info_filler", "param": {"field_name": "client_device_info"}}]}}}, {"id": 90, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/follow/template", "remark": "follow template api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_activity.follow_template?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}, {"methods": ["DELETE"], "target": "luckyvideo.core.bff_activity.unfollow_template?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "ip"}, {"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 91, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/report/user_profile", "remark": "report user_profile api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.biz.batch_report?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "auth"}, {"name": "message_conv"}]}}}, {"id": 92, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/feedback/mms", "remark": "feedback mms api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_safety.feedback?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 93, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/report/music", "remark": "report music api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_safety.report_music?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "reporter_id"}}]}}}, {"id": 94, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/report/reply", "remark": "report reply api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_safety.report_reply?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "reporter_id"}}]}}}, {"id": 95, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/user/list", "remark": "get user list api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_user.get_user_list?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "self_user_id"}}]}}}, {"id": 96, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/like/post", "remark": "like post api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_activity.like_post?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}, {"methods": ["DELETE"], "target": "luckyvideo.core.bff_activity.unlike_post?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "ip"}, {"name": "user_id_filler", "param": {"field_name": "user_id"}}, {"name": "client_device_info_filler", "param": {"field_name": "client_device_info"}}, {"name": "tss_data_filler", "param": {"field_name": "tss_data"}}]}}}, {"id": 97, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/like/reply", "remark": "like reply api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_activity.like_reply?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}, {"methods": ["DELETE"], "target": "luckyvideo.core.bff_activity.unlike_reply?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 98, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/follow/music", "remark": "follow music api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_activity.follow_music?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}, {"methods": ["DELETE"], "target": "luckyvideo.core.bff_activity.unfollow_music?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 99, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/like/comment", "remark": "like comment api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_activity.like_comment?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}, {"methods": ["DELETE"], "target": "luckyvideo.core.bff_activity.unlike_comment?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 100, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/ecommerce/post_claimed_voucher", "remark": "ecommerce", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_ecommerce.get_post_claimed_voucher?<cid>", "timeout": 2000, "category": "read", "need_login": true, "need_metas": false, "meta_list": ["userid"], "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 101, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/tracking/user_behavior_report", "remark": "core.tracking service api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.tracking.user_behavior_report?<cid>", "timeout": 5000, "category": "write", "need_login": false, "need_metas": false, "meta_list": ["userid", "deviceid", "session_id", "rn_version", "app_version"], "need_headers": false, "header_list": ["requestinfo", "requestinfo-enc"], "need_cookies": false}], "ext_info": {}}, {"id": 102, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/report/list", "remark": "get report list", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.retrieve.get_report_list?<cid>", "timeout": 2000, "category": "read", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false, "meta_list": ["userid"], "int_params": ["object_id", "report_reason_id", "object_type", "limit"]}], "ext_info": {}}, {"id": 103, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/resource/music/tab/data", "remark": "get music tab data api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_music_tab_data_v2?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["limit", "biz_type"]}], "ext_info": {}}, {"id": 104, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/music/classified/list", "remark": "get music classified list api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_music_classified_list?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["classification_id", "limit"]}], "ext_info": {}}, {"id": 105, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/resource/music/tabs", "remark": "get resource music tabs api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_music_tabs_v2?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["biz_type"]}], "ext_info": {}}, {"id": 107, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/user/name/check", "remark": "user name check api", "status": 2, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_user.check_username?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "need_login": true}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 108, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/user/detail", "remark": "user detail api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_user.get_user_detail?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "meta_list": ["userid"], "need_headers": false, "need_cookies": false, "int_params": ["user_id", "with_product_tab_entry", "query_chat"], "bool_params": ["need_friends_status_switch", "with_ls_tab"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}, {"name": "qps", "param": {"sliding_size": 5, "qps_limit": 55}}, {"name": "app_header", "remark": "app header data", "param": {"params": ["client_ip"]}}]}}}, {"id": 109, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/user/ig_updated", "remark": "user ig_updated api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_user.user_ig_updated?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "need_login": true}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 110, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/user/block", "remark": "user block and unblock api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_user.block_user?<cid>", "timeout": 2000, "category": "write", "need_metas": false, "need_headers": false, "need_cookies": false, "need_login": true}, {"methods": ["DELETE"], "target": "luckyvideo.core.bff_user.unblock_user?<cid>", "timeout": 2000, "category": "write", "need_metas": false, "need_headers": false, "need_cookies": false, "need_login": true}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "self_user_id"}}]}}}, {"id": 111, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/user/blocked_user_list", "remark": "user blocked_user_list api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_user.get_blocked_user_list?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "need_login": true}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "self_user_id"}}]}}}, {"id": 112, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/user/clear_reset", "remark": "user clear_reset api", "status": 2, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_user.clear_user_reset?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "need_login": true}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}, {"name": "defaule_value_filler", "param": {"field_name": "is_reset", "type": "bool", "value": "false"}}]}}}, {"id": 113, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/user/update", "remark": "user update api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_user.update_user_profile?<cid>", "timeout": 2000, "category": "write", "need_metas": false, "need_headers": false, "need_cookies": false, "need_login": true}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 114, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/report/comment", "remark": "report comment api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_safety.report_comment?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "reporter_id"}}]}}}, {"id": 115, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/report/hashtag", "remark": "report hashtag api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_safety.report_hashtag?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "reporter_id"}}]}}}, {"id": 116, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/report/post/unblock", "remark": "report post unblock api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_safety.block_post?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 117, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/comment/pin", "remark": "comment pin api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_activity.pin_comment?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}, {"methods": ["DELETE"], "target": "luckyvideo.core.bff_activity.unpin_comment?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "ip"}, {"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 118, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/comment/reply", "remark": "comment reply api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_activity.add_reply?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}, {"methods": ["DELETE"], "target": "luckyvideo.core.bff_activity.del_reply?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "ip"}, {"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 119, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/follow/magic", "remark": "follow magic api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_activity.follow_magic?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}, {"methods": ["DELETE"], "target": "luckyvideo.core.bff_activity.unfollow_magic?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "ip"}, {"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 120, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/follow/hashtag", "remark": "follow hashtag api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_activity.follow_hashtag?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}, {"methods": ["DELETE"], "target": "luckyvideo.core.bff_activity.unfollow_hashtag?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "ip"}, {"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 121, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/follow/user/list", "remark": "follow user list api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_activity.follow_user_list?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "from_user_id"}}]}}}, {"id": 122, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/comment/comment", "remark": "add comment api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_activity.add_comment?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}, {"methods": ["DELETE"], "target": "luckyvideo.core.bff_activity.del_comment?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 123, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/voucher/seller_vouchers", "remark": "get seller_vouchers  api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_ecommerce.get_seller_voucher_list?<cid>", "timeout": 2000, "category": "read", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 124, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/voucher/generate_token", "remark": "voucher generate_token  api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_ecommerce.generate_voucher_token?<cid>", "timeout": 2000, "category": "write", "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 125, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/ecommerce/user_item_list", "remark": "user_item_list  api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_ecommerce.get_user_item_list?<cid>", "timeout": 2000, "category": "read", "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["limit", "request_user_id"]}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 126, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/post/precheck", "remark": "post precheck api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_activity.pre_check_post?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "meta_list": ["userid"], "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "ip"}, {"name": "downgrade", "param": {"downgrade_type": 2}}, {"name": "client_device_info_filler", "param": {"field_name": "client_device_info"}}, {"name": "tss_data_filler", "param": {"field_name": "tss_data"}}]}}}, {"id": 127, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/post/create", "remark": "post create api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_activity.create_post?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "meta_list": ["userid"], "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "ip"}, {"name": "downgrade", "param": {"downgrade_type": 1}}, {"name": "client_device_info_filler", "param": {"field_name": "client_device_info"}}, {"name": "tss_data_filler", "param": {"field_name": "tss_data"}}]}}}, {"id": 128, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/post", "remark": "delete post api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["DELETE"], "target": "luckyvideo.core.bff_activity.delete_post?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 129, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/text/precheck", "remark": "text precheck api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_activity.check_sensitive_words?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {}}, {"id": 130, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/post/pin", "remark": "pin post api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_activity.pin_post?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}, {"id": 131, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/post/unpin", "remark": "unpin post api", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["POST"], "target": "luckyvideo.core.bff_activity.unpin_post?<cid>", "timeout": 2000, "category": "write", "need_login": true, "need_metas": false, "need_headers": false, "need_cookies": false}], "ext_info": {"plugin": {"enable": true, "items": [{"name": "user_id_filler", "param": {"field_name": "user_id"}}]}}}]