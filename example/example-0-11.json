{"id": 11, "target_type": 0, "cgi_type": 0, "prefix": "/", "path": "/resource/sticker/list", "remark": "sticker", "status": 0, "author": "<EMAIL>", "service_id": 0, "rules": [{"methods": ["GET"], "target": "luckyvideo.core.bff_resource.get_sticker_list_v2?<cid>", "timeout": 2000, "category": "read", "need_queries": false, "need_metas": false, "need_headers": false, "need_cookies": false, "int_params": ["biz_type"]}], "ext_info": {"mock": {"enable": true, "rule": [{"content_type": 1, "data": "", "match": {"path": ["a.b", "c.d"], "data": ""}}]}, "rule_conv": {"enable": true, "inputs": [{"type": "input type", "path": "input path", "data": "input data"}], "outputs": [{"type": "output type", "path": "output path", "data": "output data"}]}, "cache": {"enable": true, "cache": {"cache_key_prefix": "cache_key_prefix", "cache_type": 1, "cache_expire": 666, "cache_by_param_keys": ["post_id"]}}, "cgi_merge": {"enable": true, "items": [{"rule_id": 1}]}}}