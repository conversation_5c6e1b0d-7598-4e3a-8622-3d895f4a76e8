# Setup command that allows failures
.PHONY: init
init:
	@.ci-scripts/scripts/init.sh

.PHONY: help
help:
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'

.PHONY: build
build.%: ## build service or build.xxxx
	@.ci-scripts/scripts/build.sh $*

build: ## build service
	@.ci-scripts/scripts/build.sh

.PHONY: run
run.%:
	@.ci-scripts/scripts/run.sh $*

run: ## run service
	@.ci-scripts/scripts/run.sh

.PHONY: lint
lint: ## run golangci linter
	@.ci-scripts/scripts/lint.sh $(fast-mode) $(full-lint)

.PHONY: unit-test
unit-test: ## run unit tests
	@.ci-scripts/scripts/test.sh 

.PHONY: test
test: ## run unit tests
	@.ci-scripts/scripts/test.sh

.PHONY: build-proto
build-proto: ## Run build-proto scripts to build proto go file
	@.ci-scripts/scripts/build_proto.sh

.PHONY: build-with-proto
build-with-proto: init build-proto## Run build-with-proto scripts to build proto go file and service
	@.ci-scripts/scripts/build.sh

.PHONY: build-with-proto
build-with-proto.%: init build-proto## Run build-with-proto scripts to build proto go file and service
	@.ci-scripts/scripts/build.sh $*

.PHONY: install-dep 
install-dep: ## Run install-dep to install dependencies
	@.ci-scripts/scripts/install_dep.sh

.PHONY: update ci rules
update-submodule: ## Run update-submodule to update submoduels
	@.ci-scripts/scripts/update-submodule.sh 

ifneq ("$(wildcard Makefile-extend.mk)","")
include Makefile-extend.mk
endif 

.DEFAULT_GOAL:= help
