package utils

import (
	"context"

	"git.garena.com/shopee/platform/service-governance/viewercontext"
	"git.garena.com/shopee/platform/service-governance/viewercontext/attr"
	"git.garena.com/shopee/video/core/common/logutil"
)

func PreparePFBCtx(ctx context.Context, pfb string) context.Context {
	ctx, err := viewercontext.Start(
		ctx,
		attr.WithPFB(pfb),
	)
	if err != nil {
		logutil.GetLogger(ctx).WithError(err).Error("start pfb view context error")
		return ctx
	}
	return ctx
}
