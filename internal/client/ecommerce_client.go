package client

import (
	"context"

	"git.garena.com/shopee/video/core/backend/bff_activity/gen/go/luckyvideo_core_ecommerce.pb/luckyvideo/core/ecommerce"
	"git.garena.com/shopee/video/core/common/errutil"
	"git.garena.com/shopee/video/core/common/logutil"
	"git.garena.com/shopee/video/core/common/spex"
)

type ecommerceAPIClientStruct struct {
}

var EcommmereAPIClient = ecommerceAPIClientStruct{}

func (u *ecommerceAPIClientStruct) PostItemReplacedConfirm(ctx context.Context, req *ecommerce.PostItemReplacedConfirmRequest) uint32 {
	logger := logutil.GetLogger(ctx).WithField("req", req)
	resp := new(ecommerce.PostItemReplacedConfirmResponse)
	code := spex.RegionRPCRequest(ctx, "luckyvideo.core.ecommerce.post_item_replaced_confirm", req, resp)
	if code != errutil.Success {
		logger.With<PERSON>ield("code", code).With<PERSON><PERSON>("resp", resp).Error("call post_item_replaced_confirm failed")
		return code
	}
	return code
}
