package infra

import (
	"context"
	"errors"
	"fmt"
	"log"
	"sync"

	"git.garena.com/shopee/video/core/backend/bff_ecommerce/internal/infra/config"
	"git.garena.com/shopee/video/core/common/cid"
	"git.garena.com/shopee/video/core/common/cid/cidconfig"
	commconfig "git.garena.com/shopee/video/core/common/config"
	"git.garena.com/shopee/video/core/common/config/provider"
	"git.garena.com/shopee/video/core/common/dynamicconfig"
	"git.garena.com/shopee/video/core/common/env"
	"git.garena.com/shopee/video/core/common/logutil"
	"git.garena.com/shopee/video/core/common/spex"
	"git.garena.com/shopee/video/core/common/xboot"
)

const (
	ConfigKeyMetaSwitch   = "luckyvideo_meta_switch_config"
	ConfigKeyAbTestConfig = "abtest_config"
	ConfigCommandChain    = "command_chain_config"
)

var configSourcePairs = map[config.Source]config.Pairs{
	config.SpexConfig: {
		// multi server
		{Module: env.GetModule(), Key: "server_config", ValueProto: &xboot.ServerConfig{}},
		{Module: env.GetModule(), Key: ConfigKeyMetaSwitch, ValueProto: &MetaSwitchConfig{}},
		{Module: env.GetModule(), Key: "feature_toggle", ValueProto: &FeatureToggleDector{}, Mapper: config.Mapper(
			func(from interface{}) (to interface{}, err error) {
				dector, ok := from.(*FeatureToggleDector)
				if !ok || dector == nil {
					return nil, errors.New("config not exist or invalid")
				}

				dector.tailNumbers = make(map[string]*commconfig.TailNumberConfig, len(dector.TailNumbers))
				for feature, tailNumberStr := range dector.TailNumbers {
					dector.tailNumbers[feature], err = commconfig.ParseTailNumberConfig(tailNumberStr)
					if err != nil {
						return nil, err
					}
				}

				return dector, nil
			})},
		{Module: env.GetModule(), Key: ConfigKeyAbTestConfig, ValueProto: &AbConfigs{}},
		{Module: env.GetModule(), Key: ConfigCommandChain, ValueProto: &spex.ClientConfig{}, Mapper: func(region string) provider.ConfigMapper {
			return func(from interface{}) (to interface{}, err error) {
				ctx := cid.WithCID(context.Background(), region)
				chainConfigs := from.(*spex.ClientConfig)
				err = spex.MustUpdateClientConfig(chainConfigs)
				if err != nil {
					logutil.GetLogger(ctx).WithError(err).Error("MustUpdateClientConfig error")
					return nil, err
				}
				return chainConfigs, nil
			}
		}},
	},
}

func NewConfigRegistry() (registry config.Registry, delayChecker func()) {
	cc := initCC()
	initDefaultServeCIDInfo(cc)

	// should renew cc after initCID set default CID
	r := config.New([]config.Source{config.ConfigCenter, config.SpexConfig}, config.WithCC(cc))
	for configSource, configPairs := range configSourcePairs {
		modelName := env.GetModule()
		if configSource == config.ConfigCenter {
			modelName += "_service"
		}

		moduleConfigPairs := configPairs.FetchModule(modelName)
		if len(moduleConfigPairs) == 0 {
			continue
		}

		if err := r.Registe(configSource, moduleConfigPairs); err != nil {
			log.Fatalf("NewConfigRegistry registe error: %v", err)
		}
	}

	var checkOnce sync.Once
	return r, func() {
		checkOnce.Do(func() {
			if err := r.Check(); err != nil {
				log.Fatalf("NewConfigRegistry check error: %v", err)
			}
		})
	}

}

func initCC() dynamicconfig.ConfigCenter {
	return dynamicconfig.InitConfigCenter(fmt.Sprintf("./conf/config_%s.ini", env.GetEnv()))
}

func initDefaultServeCIDInfo(cc dynamicconfig.ConfigCenter) {
	// 1. init serve cid config
	cidConfig := cidconfig.NewServeCidInfoFromConfig(provider.NewCcConfig(cc, "serve_cid_info", "luckyvideo-bffecommerce", &cidconfig.ServeCidInfoStruct{}))
	if len(cidConfig.GetServeCidList()) == 0 {
		log.Fatal("CidConfig miss")
	}

	// 2. set default cid before service config and resource init
	// If not, common resource will not init multi cid
	cidconfig.SetDefaultServeCidInfo(cidConfig)
}

type LogLevelConfig struct {
	Level string `json:"level"`
}

type FeatureToggleDector struct {
	TailNumbers map[string]string `json:"tail_numbers"`
	BoolToggles map[string]bool   `json:"bool_toggles"`

	tailNumbers map[string]*commconfig.TailNumberConfig
}

func (f *FeatureToggleDector) IsToggleOn(feature string) bool {
	if f != nil {
		if boolToggle, ok := f.BoolToggles[feature]; ok {
			return boolToggle
		}
	}

	return false
}
