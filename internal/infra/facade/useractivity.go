package facade

import (
	"context"

	itemlikepb "git.garena.com/shopee/video/core/backend/bff_ecommerce/gen/go/user_activity_item_like.pb"
	"git.garena.com/shopee/video/core/common/errs"
	"git.garena.com/shopee/video/core/common/errutil"
	"git.garena.com/shopee/video/core/common/spex"
)

type UserActivityClient struct {
}

func NewUserActivityClient() *UserActivityClient {
	return &UserActivityClient{}
}

func (c UserActivityClient) SetUserItemLike(ctx context.Context, req *itemlikepb.SetUserItemLikeRequest) error {
	rsp := &itemlikepb.SetUserItemLikeResponse{}

	code := spex.GlobalRPCRequest(ctx, "user_activity.item_like.set_user_item_like", req, rsp)
	if code != errutil.Success {
		return errs.New(code, "rpc set_user_item_like err. %+v %v", req, rsp.GetDebugMsg())
	}

	return nil
}
