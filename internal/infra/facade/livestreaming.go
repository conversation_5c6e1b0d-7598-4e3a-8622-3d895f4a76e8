package facade

import (
	"context"

	livegwpb "git.garena.com/shopee/video/core/backend/bff_ecommerce/gen/go/live_streaming_gateway.pb"
	"git.garena.com/shopee/video/core/common/errs"
	"git.garena.com/shopee/video/core/common/errutil"
	"git.garena.com/shopee/video/core/common/spex"
)

type LiveStreamingServiceClient interface {
	GetMiniFeedLiveInfo(ctx context.Context, req *livegwpb.GetMiniFeedLiveInfoRequest) (*livegwpb.GetMiniFeedLiveInfoResponse, error)
}

var DefaultLiveStreamingClient = &liveStreamingClient{}

type liveStreamingClient struct {
}

func (*liveStreamingClient) GetMiniFeedLiveInfo(ctx context.Context, req *livegwpb.GetMiniFeedLiveInfoRequest) (*livegwpb.GetMiniFeedLiveInfoResponse, error) {
	rsp := &livegwpb.GetMiniFeedLiveInfoResponse{}

	if code := spex.RegionRPCRequest(ctx, "live_streaming.gateway.get_mini_feed_live_info", req, rsp); code != errutil.Success {
		return nil, errs.New(code, "rpc: get_mini_feed_live_info failed. %v", req.GetItemId())
	}
	return rsp, nil
}
