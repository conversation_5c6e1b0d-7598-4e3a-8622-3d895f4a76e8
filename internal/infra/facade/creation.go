package facade

import (
	"context"

	creationpb "git.garena.com/shopee/video/core/backend/bff_ecommerce/gen/go/luckyvideo_supply_creation_tool_server.pb"
	"git.garena.com/shopee/video/core/common/errs"
	"git.garena.com/shopee/video/core/common/errutil"
	"git.garena.com/shopee/video/core/common/spex"
)

type CreationClient struct {
}

func NewCreationClient() *CreationClient {
	return &CreationClient{}
}

func (*CreationClient) GetFittingRoomInfo(ctx context.Context, req *creationpb.GetItemFittingInfoRequest) (*creationpb.GetItemFittingInfoResponse, error) {
	rsp := &creationpb.GetItemFittingInfoResponse{}
	code := spex.GlobalRPCRequest(ctx, "luckyvideo.supply.creation_tool_server.get_item_fitting_info", req, rsp)
	if code != errutil.Success {
		return nil, errs.New(code, "call get_item_fitting_info failed. item_id = %v", req.GetItemId())
	}
	return rsp, nil
}
