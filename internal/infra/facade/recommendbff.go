package facade

import (
	"context"
	"strings"

	recommendbffpb "git.garena.com/shopee/video/core/backend/bff_ecommerce/gen/go/recommend_bff.pb"
	"git.garena.com/shopee/video/core/common/env"
	"git.garena.com/shopee/video/core/common/errs"
	"git.garena.com/shopee/video/core/common/errutil"
	"git.garena.com/shopee/video/core/common/json"
	"git.garena.com/shopee/video/core/common/logutil"
	reporter "git.garena.com/shopee/video/core/common/reporter_lib"
	"git.garena.com/shopee/video/core/common/spex"
	"github.com/golang/protobuf/proto"
)

type RecommendBffClient interface {
	SimilarProductRcmdBanner(ctx context.Context, req *recommendbffpb.SimilarProductRcmdBannerRequest) (*recommendbffpb.SimilarProductRcmdBannerResponse, error)
}

type defaultRecommendBffClient struct{}

func NewRecommendBffClient() RecommendBffClient {
	if env.InLive() {
		return &defaultRecommendBffClient{}
	}

	return &mockRecommendBffClient{}
}

func (c defaultRecommendBffClient) SimilarProductRcmdBanner(ctx context.Context, req *recommendbffpb.SimilarProductRcmdBannerRequest) (*recommendbffpb.SimilarProductRcmdBannerResponse, error) {
	rsp, ierr := c.doSimilarProductRcmdBannerRequest(ctx, req)
	if ierr == nil {
		return rsp, nil
	}

	if reason := c.skipErr(ctx, ierr); reason != "" {
		logutil.GetRateLogger(ctx, 1).WithError(ierr).WithField("errType", reason).Info("[similar item] skip bff internal err")
		return &recommendbffpb.SimilarProductRcmdBannerResponse{}, nil
	}

	return nil, ierr
}

func (c defaultRecommendBffClient) skipErr(ctx context.Context, ierr errs.IError) (reason string) {
	defer func() {
		errType := "others"
		if reason != "" {
			errType = reason
		}

		_ = reporter.ReportCounterContext(ctx, "similar_cheaper_err_total", 1, reporter.NewLabel("errType", errType))
	}()

	muteMsgs := []string{"empty_mixer_resp", "has_cheapest_label", "similar_product_rcmd_exp_not_found", "match_target_shop_filter", "not_reached_minimum_item_number"}
	// 下游都是统一的错误码，大多数商品没cheaper，也会返回该错误码，这里抹掉做个上报&日志
	if ierr.Code() == uint32(recommendbffpb.Constant_ERROR_BFF_INTERNAL) {
		for _, m := range muteMsgs {
			if strings.Contains(ierr.Msg(), m) {
				reason = m
				break
			}
		}
	}

	return
}

func (c defaultRecommendBffClient) doSimilarProductRcmdBannerRequest(ctx context.Context, req *recommendbffpb.SimilarProductRcmdBannerRequest) (*recommendbffpb.SimilarProductRcmdBannerResponse, errs.IError) {
	rsp := &recommendbffpb.SimilarProductRcmdBannerResponse{}

	code := spex.GlobalRPCRequest(ctx, "recommend.bff.similar_cheaper_rcmd_banner", req, rsp)
	if code != errutil.Success {
		return nil, errs.New(code, "rpc: recommend.bff.similar_cheaper_rcmd_banner failed. %v %v", req.GetItemid(), rsp.GetErrMsg())
	}

	if rsp.GetStatus() != 0 {
		return nil, errs.New(uint32(rsp.GetStatus()), "similar_cheaper_rcmd_banner status invalid. %v %v", req.GetItemid(), rsp.GetErrMsg())
	}
	return rsp, nil
}

type mockRecommendBffClient struct {
}

func (c mockRecommendBffClient) SimilarProductRcmdBanner(ctx context.Context, req *recommendbffpb.SimilarProductRcmdBannerRequest) (*recommendbffpb.SimilarProductRcmdBannerResponse, error) {
	raws, _ := json.Marshal(req)
	logutil.GetLogger(ctx).WithField("req", string(raws)).Info("[mock] received req, return mock data")
	return &recommendbffpb.SimilarProductRcmdBannerResponse{
		Data: []*recommendbffpb.ItemCardItemFull{
			{
				Itemid: proto.Int64(2303543647),
				Shopid: proto.Int64(511046),
				ItemCardDisplayPrice: &recommendbffpb.ItemCardDisplayPrice{
					ModelId: proto.Uint64(10025179155),
					Price:   proto.Int64(10000000),
				},
			},
		},
	}, nil
}
