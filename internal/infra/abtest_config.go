package infra

import (
	"context"

	"git.garena.com/shopee/experiment-platform/abtest-core/v2/api"
	ab_config "git.garena.com/shopee/experiment-platform/abtest-core/v2/api/config"
	"git.garena.com/shopee/video/core/backend/bff_ecommerce/internal/infra/config"
	"git.garena.com/shopee/video/core/common/cid"
)

const (
	AbFloatingWindowFilterVoucher = "floating_window_filter_voucher"
)

type AbConfigs map[string]*AbConfig

func (c AbConfigs) Scenes() []string {
	dup := make(map[string]struct{})
	rs := make([]string, 0, 8)
	for _, v := range c {
		if _, ok := dup[v.SceneKey]; ok {
			continue
		}
		dup[v.SceneKey] = struct{}{}
		rs = append(rs, v.SceneKey)
	}

	return rs
}

func (c AbConfigs) Get(bizName string) *AbConfig {
	return c[bizName]
}

type AbConfig struct {
	SceneKey string `json:"scene_key"`
	LayerKey string `json:"layer_key"`
}

func GetAbConfigs(ctx context.Context, reg config.Registry) *AbConfigs {
	return reg.Get(ctx, ConfigKeyAbTestConfig).(*AbConfigs)
}

func InitAbClient(reg config.Registry) {
	ctx := cid.ContextForGlobalDaemon(context.Background()) // ignore bg ctx
	conf := &ab_config.Config{
		AbtestConfig: ab_config.AbtestConfig{
			SceneKeys: GetAbConfigs(ctx, reg).Scenes(),
		},
	}
	api.Init(api.Config(conf))
}
