package service

import (
	"context"
	"strconv"

	"git.garena.com/shopee/video/core/backend/bff_ecommerce/gen/bffutil"
	bffecommercepb "git.garena.com/shopee/video/core/backend/bff_ecommerce/gen/go/luckyvideo_core_bff_ecommerce.pb"
	"git.garena.com/shopee/video/core/backend/bff_ecommerce/internal/infra"
	"git.garena.com/shopee/video/core/backend/bff_ecommerce/internal/infra/config"
	"git.garena.com/shopee/video/core/common/bff/parser"
	reporter "git.garena.com/shopee/video/core/common/reporter_lib"
)

const (
	defaultMetricsReportKey = "cmd_replace_metrics"
)

const (
	defaultReportTypeDeviceInfo = "device_info"
)

type PluginReplaceService struct {
	Registry config.Registry
}

func (p *PluginReplaceService) isEnableDeviceInfoReplacer(ctx context.Context) bool {
	return infra.GetMetaSwitchConfig(ctx, p.Registry).EnableDeviceInfo
}

func (p *PluginReplaceService) makeDefaultReportLabel(ctx context.Context, field string, isNew bool) []reporter.Label {
	return []reporter.Label{
		{
			Key: "report_field",
			Val: field,
		},
		{
			Key: "is_new",
			Val: strconv.FormatBool(isNew),
		},
	}
}

func (p *PluginReplaceService) ReplaceDeviceInfo(ctx context.Context,
	meta *bffecommercepb.BffRequestMeta, origin *bffecommercepb.DeviceInfo) *bffecommercepb.DeviceInfo {

	return p.ReplaceDeviceInfoFromSearcher(ctx, bffutil.NewMetaSearch(meta), origin)
}

func (p *PluginReplaceService) ReplaceDeviceInfoFromSearcher(ctx context.Context, ss parser.IKeySearcher, origin *bffecommercepb.DeviceInfo) *bffecommercepb.DeviceInfo {
	useNewDeviceInfo := true

	defer func() {
		_ = reporter.ReportCounterContext(ctx, defaultMetricsReportKey, 1,
			p.makeDefaultReportLabel(ctx, defaultReportTypeDeviceInfo, useNewDeviceInfo)...,
		)
	}()

	if origin != nil && !p.isEnableDeviceInfoReplacer(ctx) {
		useNewDeviceInfo = false
		return origin
	}
	dev := bffutil.BuildDeviceInfoFromSearcher(ss)
	return dev
}
