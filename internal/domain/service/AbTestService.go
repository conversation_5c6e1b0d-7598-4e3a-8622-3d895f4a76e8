package service

import (
	"context"
	"fmt"
	"strings"

	"git.garena.com/shopee/experiment-platform/abtest-core/v2/api"
	"git.garena.com/shopee/experiment-platform/abtest-core/v2/api/param"
	"git.garena.com/shopee/video/core/backend/bff_ecommerce/internal/infra"
	"git.garena.com/shopee/video/core/backend/bff_ecommerce/internal/infra/config"
	"git.garena.com/shopee/video/core/common/cid"
	"git.garena.com/shopee/video/core/common/logutil"
	"git.garena.com/shopee/video/core/common/tracer"
)

type AbTestService struct {
	reg config.Registry
}

func NewAbTestService(reg config.Registry) *AbTestService {
	return &AbTestService{reg: reg}
}

func (a *AbTestService) getAbClient() *api.AbTestClient {
	return api.GetAbClient()
}

func (a *AbTestService) FilterExclusiveVoucher(ctx context.Context, userId uint64) bool {
	span := tracer.GetSpanFromContext(ctx)
	childSpan, _ := span.NewChildSpan("ABTest")
	spanLog := func(msg string) {
		if childSpan != nil {
			childSpan.LogDebugFields("filterExclusiveVoucherAb", msg)
			childSpan.Finish()
		}
		logutil.GetRateLogger(ctx, 2).WithField("filterExclusiveVoucherAb", msg).Info("ab: filter exclusive voucher result")
	}

	configs := infra.GetAbConfigs(ctx, a.reg)
	abConfig := configs.Get(infra.AbFloatingWindowFilterVoucher)
	if abConfig == nil {
		spanLog("nil ab config")
		return true
	}

	builder := param.NewExpParamBuilder().SceneKey(abConfig.SceneKey).LayerKey(abConfig.LayerKey).Region(strings.ToUpper(cid.GetCID(ctx))).UserID(int64(userId))
	hit, err := a.getAbClient().GetExpGroups(ctx, builder.Build())
	if err != nil {
		spanLog("ab error")
		// default filter voucher
		logutil.GetLogger(ctx).WithError(err).Error("ab: FilterExclusiveVoucher fail to get exp group")
		return true
	}

	for _, h := range hit.Hit {
		spanLog(fmt.Sprintf("hit group:%v", h.GroupName))
		switch h.Parameter {
		case "3":
			return true
		case "4":
			return false
		}
	}

	spanLog("not hit ab")
	return true
}
