package service

import (
	"context"
	"fmt"
	"time"

	"git.garena.com/shopee/video/core/backend/bff_activity/gen/bffutil"
	"git.garena.com/shopee/video/core/backend/bff_activity/internal/domain/repo"
	"git.garena.com/shopee/video/core/backend/bff_activity/internal/infra"
	"git.garena.com/shopee/video/core/backend/bff_activity/internal/infra/config"
	"git.garena.com/shopee/video/core/common/cid"
	"git.garena.com/shopee/video/core/common/dsutil"
	"git.garena.com/shopee/video/core/common/errs"
	"git.garena.com/shopee/video/core/common/errutil"
	"git.garena.com/shopee/video/core/common/localization"
	"git.garena.com/shopee/video/core/common/logutil"
	"git.garena.com/shopee/video/core/common/reporter_lib"
	spexMeta "git.garena.com/shopee/video/core/common/spex/meta"
	"github.com/spf13/cast"
)

const (
	CommentRateOfDay    = 1
	CommentRateOfHour   = 2
	CommentRateOfMinute = 3

	dayLayout    = "2006-01-02"
	hourLayout   = "2006-01-02 15"
	minuteLayout = "2006-01-02 15:04"

	second = 1
	minute = 60 * second
	hour   = 60 * minute

	translateTypeDay        = 3
	translateTypeHour       = 2
	translateTypeMinute     = 1
	translateTypeDefaultKey = "%d"
	translateTypeDayKey     = "%d_day"
	translateTypeHourKey    = "%d_hour"
	translateTypeMinuteKey  = "%d_minute"
)
const (
	reportFrequencyIncr  = "lucky_video_api_frequency_incr"
	reportFrequencyLimit = "lucky_video_api_frequency_limit"
)

type CommentFrequencyService struct {
	Registry      config.Registry
	FrequencyRepo *repo.FrequencyRepo
}

func (c *CommentFrequencyService) Limited(ctx context.Context, userId uint64, language string) (string, uint32) {
	conf, enable := c.isFrequencyCtrlEnable(ctx, userId)
	if !enable {
		return "", errutil.Success
	}
	if err := c.limit(ctx, conf, userId); err != nil {
		code := conf.GetFrequencyErrCode()
		defaultMsg := conf.GetFrequencyErrMsg()
		limitType := conf.FrequencyConfig.LimitType
		msg := c.getLeftTimeMsgByType(ctx, code, defaultMsg, limitType, language)
		return msg, code
	}

	c.incr(ctx, conf, userId)
	return "", errutil.Success
}

func (c *CommentFrequencyService) isFrequencyCtrlEnable(ctx context.Context, userId uint64) (*infra.CommentFrequencyOption, bool) {
	opt := c.Registry.Get(ctx, infra.ConfigKeyCommentFrequencyOption)
	if opt == nil {
		return nil, false
	}
	conf, ok := opt.(*infra.CommentFrequencyOption)
	if !ok {
		return nil, false
	}
	return conf, conf.Enabled(userId)
}

func (c *CommentFrequencyService) limit(ctx context.Context, conf *infra.CommentFrequencyOption, userId uint64) error {
	keyIdent := c.FrequencyRepo.GetWindowCommentFrequencyKeyIdent(ctx, conf.FrequencyConfig.LimitType, userId)
	count, err := c.FrequencyRepo.GetWindowFrequency(ctx, keyIdent)
	if err != nil {
		logutil.GetLogger(ctx).WithError(err).Error("GetWindowFrequency error")
		c.reportErr(ctx, errGet)
		return nil
	}
	if count > conf.FrequencyConfig.Upper {
		c.reportLimit(ctx, conf.FrequencyConfig.LimitType, conf.FrequencyConfig.Upper, statusLimit)
		return errs.New(infra.ErrReachRateCommonCode, "reach rate: %d=>%d", count, conf.FrequencyConfig.Upper)
	}
	c.reportLimit(ctx, conf.FrequencyConfig.LimitType, 0, statusNormal)
	return nil
}

func (c *CommentFrequencyService) getLeftTimeMsgByType(ctx context.Context, code uint32, defaultMsg string, limitType uint32, language string) string {
	args := c.leftTime(ctx, limitType)
	var msg string
	switch len(args) {
	case translateTypeDay:
		msg = bffutil.CodeToMsgByKey(ctx, fmt.Sprintf(translateTypeDayKey, code), language)
	case translateTypeHour:
		msg = bffutil.CodeToMsgByKey(ctx, fmt.Sprintf(translateTypeHourKey, code), language)
	case translateTypeMinute:
		msg = bffutil.CodeToMsgByKey(ctx, fmt.Sprintf(translateTypeMinuteKey, code), language)
	default:
		msg = bffutil.CodeToMsgByKey(ctx, fmt.Sprintf(translateTypeDefaultKey, code), language)
	}
	if len(msg) == 0 {
		return ""
	}
	if len(args) == 0 {
		return msg
	}
	return fmt.Sprintf(msg, args...)
}

func (c *CommentFrequencyService) leftTime(ctx context.Context, limitType uint32) []interface{} {
	nowTime := dsutil.UTCToLocalContext(ctx, time.Now())
	switch limitType {
	case CommentRateOfDay:
		// xxhxxmxxs
		t, _ := c.byLayout(ctx, dayLayout, nowTime)
		leftSeconds := c.leftSeconds(t, nowTime, 24*time.Hour)
		leftSecondsDivided := leftSeconds % hour
		return c.castZero([]int64{leftSeconds / hour, leftSecondsDivided / minute, leftSecondsDivided % minute})
	case CommentRateOfHour:
		// xxmxxs
		t, _ := c.byLayout(ctx, hourLayout, nowTime)
		leftSeconds := c.leftSeconds(t, nowTime, time.Hour)
		return c.castZero([]int64{leftSeconds / minute, leftSeconds % minute})
	case CommentRateOfMinute:
		// xxs
		t, _ := c.byLayout(ctx, minuteLayout, nowTime)
		leftSeconds := c.leftSeconds(t, nowTime, time.Minute)
		return c.castZero([]int64{leftSeconds})
	default:
		return []interface{}{}
	}
}

func (c *CommentFrequencyService) byLayout(ctx context.Context, layout string, t time.Time) (time.Time, error) {
	loc := localization.GetLocation(cid.GetCID(ctx))
	locTime := t.In(loc)
	return time.ParseInLocation(layout, locTime.Format(layout), loc)
}

func (c *CommentFrequencyService) castZero(args []int64) []interface{} {
	var filtered []int64
	for i, arg := range args {
		if arg == 0 {
			continue
		}
		filtered = args[i:]
		break
	}
	results := make([]interface{}, 0, len(filtered))
	for _, elem := range filtered {
		results = append(results, elem)
	}
	return results
}

func (c *CommentFrequencyService) leftSeconds(t time.Time, nowTime time.Time, multi time.Duration) int64 {
	return int64(t.Add(multi).Sub(nowTime).Seconds())
}

func (c *CommentFrequencyService) reportErr(ctx context.Context, errType string) {
	_ = reporter_lib.ReportCounterContext(ctx, reportFrequencyIncr, 1, []reporter_lib.Label{{Key: "type", Val: errType}}...)
}

// reportLimit for `count`, should report 0 when limit is free, otherwise report the threshold.
func (c *CommentFrequencyService) reportLimit(ctx context.Context, limitType uint32, count uint32, status string) {

	labels := []reporter_lib.Label{
		{Key: "route", Val: spexMeta.CommandFromContext(ctx)},
		{Key: "type", Val: cast.ToString(limitType)},
		{Key: "status", Val: status},
		{Key: "count", Val: cast.ToString(count)},
	}
	_ = reporter_lib.ReportCounterContext(ctx, reportFrequencyLimit, 1, labels...)
}

func (c *CommentFrequencyService) incr(ctx context.Context, conf *infra.CommentFrequencyOption, userId uint64) {
	keyIdent := c.FrequencyRepo.GetWindowCommentFrequencyKeyIdent(ctx, conf.FrequencyConfig.LimitType, userId)
	err := c.FrequencyRepo.IncrWindowFrequency(ctx, []*repo.FrequencyKeyIdent{keyIdent})
	if err != nil {
		logutil.GetLogger(ctx).WithError(err).Error("IncrWindowFrequency error")
		c.reportErr(ctx, errIncr)
	}
}
