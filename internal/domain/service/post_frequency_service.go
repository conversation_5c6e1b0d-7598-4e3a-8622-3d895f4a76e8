package service

import (
	"context"
	"strconv"

	bffactivitypb "git.garena.com/shopee/video/core/backend/bff_activity/gen/go/luckyvideo_core_bff_activity.pb"
	"git.garena.com/shopee/video/core/backend/bff_activity/internal/domain/repo"
	"git.garena.com/shopee/video/core/backend/bff_activity/internal/infra"
	"git.garena.com/shopee/video/core/backend/bff_activity/internal/infra/config"
	"git.garena.com/shopee/video/core/common/contextx"
	"git.garena.com/shopee/video/core/common/errgroup"
	"git.garena.com/shopee/video/core/common/errs"
	"git.garena.com/shopee/video/core/common/errutil"
	"git.garena.com/shopee/video/core/common/logutil"
	"git.garena.com/shopee/video/core/common/reporter_lib"
	"github.com/spf13/cast"
)

const (
	reportPostFrequencyLimit = "lucky_video_post_frequency_limit"
	reportPostFrequencyErr   = "lucky_video_post_frequency_err"

	PostLimitTypeCommerce = 1

	errIncr = "1"
	errGet  = "2"

	statusNormal = "normal"
	statusLimit  = "limit"

	LimitFromCreate   = "create"
	LimitFromPreCheck = "precheck"

	PositionAfterParam  = 0
	PositionAfterCreate = 1
)

type FreqOption struct {
	Cfg           *infra.PostFrequencyOption
	PostLimitType int
	DeviceId      string
	UserId        uint64
	NeedCount     bool
	LimitFrom     string
}

func (o *FreqOption) getObjectId() (string, error) {
	if o == nil {
		return "", errs.New(errutil.ErrParam, "err freq config")
	}
	if o.UserId > 0 {
		return strconv.FormatUint(o.UserId, 10), nil
	}
	return "", errs.New(errutil.ErrParam, "device id and user id can not be empty both")
}

type PostFrequencyService struct {
	FreqRepo *repo.FrequencyRepo
	Registry config.Registry
}

// Limit
/**
1. 根据user id进行频控
2. 多配置需同时限制。
*/
func (p *PostFrequencyService) Limit(ctx context.Context, opt *FreqOption) error {
	logger := logutil.GetLogger(ctx)
	objectId, err := opt.getObjectId()
	if err != nil {
		logger.WithError(err).Error("get object error")
		return nil // 不限制
	}
	if !opt.Cfg.Enabled() {
		return nil //未开启
	}
	// 根据配置是否限制
	wg := errgroup.WithContext(ctx)
	for _, frequencyOption := range opt.Cfg.FrequencyOption {
		frequencyOption := frequencyOption
		wg.Go(func(ctx context.Context) error {
			if !p.optionEnabled(frequencyOption, opt) {
				return nil
			}
			return p.isLimited(ctx, frequencyOption, objectId, opt.LimitFrom)
		})
	}
	err = wg.Wait()
	if err != nil {
		return err
	}

	if !opt.NeedCount {
		return nil
	}
	if !opt.Cfg.ShouldCount(PositionAfterParam) {
		return nil
	}
	// 异步更新
	go p.Incr(contextx.RemoveDeadline(ctx), opt)
	return nil
}

func (p *PostFrequencyService) Incr(ctx context.Context, opt *FreqOption) {
	if !opt.Cfg.Enabled() {
		return //未开启
	}
	objectIds := []string{strconv.FormatUint(opt.UserId, 10)}
	if len(opt.DeviceId) > 0 {
		objectIds = append(objectIds, opt.DeviceId)
	}
	idents := make([]*repo.FrequencyKeyIdent, 0, 6)
	for _, freqCfg := range opt.Cfg.FrequencyOption {
		for _, objectId := range objectIds {
			if !p.optionEnabled(freqCfg, opt) {
				continue
			}
			idents = append(idents, p.FreqRepo.GetWindowPostFrequencyKeyIdent(ctx,
				freqCfg.TimeLimitType, freqCfg.PostLimitType, objectId))
		}
	}
	err := p.FreqRepo.IncrWindowFrequency(ctx, idents)
	if err != nil {
		logutil.GetLogger(ctx).WithError(err).Error("IncrWindowFrequency error")
		p.reportErr(ctx, errIncr)
	}
}

func (p *PostFrequencyService) isLimited(ctx context.Context, frequencyOption *infra.FrequencyOption, objectId string, from string) error {
	logger := logutil.GetLogger(ctx)
	count, err := p.FreqRepo.GetWindowFrequency(ctx,
		p.FreqRepo.GetWindowPostFrequencyKeyIdent(ctx, frequencyOption.TimeLimitType, frequencyOption.PostLimitType, objectId))
	if err != nil {
		logger.WithError(err).Error("isLimited GetWindowFrequency error")
		p.reportErr(ctx, errGet)
		return nil
	}
	if count >= frequencyOption.Upper {
		p.reportLimit(ctx, frequencyOption.TimeLimitType, frequencyOption.PostLimitType, frequencyOption.Upper, statusLimit, from)
		return errs.New(uint32(bffactivitypb.Constant_API_ERROR_REACH_POST_RATE), "reach rate: %d=>%d", count, frequencyOption.Upper)
	}
	p.reportLimit(ctx, frequencyOption.TimeLimitType, frequencyOption.PostLimitType, 0, statusNormal, from)
	return nil
}

// reportLimit for `count`, should report 0 when limit is free, otherwise report the threshold.
func (p *PostFrequencyService) reportLimit(ctx context.Context, timeLimitType int, postLimitType int, count uint32, status string, from string) {
	labels := []reporter_lib.Label{
		{Key: "type_time", Val: cast.ToString(timeLimitType)},
		{Key: "type_post", Val: cast.ToString(postLimitType)},
		{Key: "status", Val: status},
		{Key: "from", Val: from},
		{Key: "count", Val: cast.ToString(count)},
	}
	_ = reporter_lib.ReportCounterContext(ctx, reportPostFrequencyLimit, 1, labels...)
}

func (p *PostFrequencyService) reportErr(ctx context.Context, errType string) {
	_ = reporter_lib.ReportCounterContext(ctx, reportPostFrequencyErr, 1, []reporter_lib.Label{{Key: "type", Val: errType}}...)
}

func (p *PostFrequencyService) optionEnabled(frequencyOption *infra.FrequencyOption, currentPostOption *FreqOption) bool {
	if frequencyOption.PostLimitType > 0 &&
		frequencyOption.PostLimitType != currentPostOption.PostLimitType {
		return false // 当前规则不适用
	}
	if !frequencyOption.Enabled(currentPostOption.UserId) {
		return false
	}
	return true
}
