package server

import (
	"context"
	"strconv"
	"time"

	commonpb "git.garena.com/shopee/sp_protocol/golang/common.pb"
	"git.garena.com/shopee/video/core/backend/bff_ecommerce/gen/autoclient"
	"git.garena.com/shopee/video/core/backend/bff_ecommerce/gen/autoconv"
	"git.garena.com/shopee/video/core/backend/bff_ecommerce/gen/bffutil"
	live_gateway "git.garena.com/shopee/video/core/backend/bff_ecommerce/gen/go/live_streaming_gateway.pb"
	bffecommercepb "git.garena.com/shopee/video/core/backend/bff_ecommerce/gen/go/luckyvideo_core_bff_ecommerce.pb"
	retrievepb "git.garena.com/shopee/video/core/backend/bff_ecommerce/gen/go/luckyvideo_retrieve.pb"
	creationpb "git.garena.com/shopee/video/core/backend/bff_ecommerce/gen/go/luckyvideo_supply_creation_tool_server.pb"
	userpb "git.garena.com/shopee/video/core/backend/bff_ecommerce/gen/go/luckyvideo_user.pb"
	recommendbffpb "git.garena.com/shopee/video/core/backend/bff_ecommerce/gen/go/recommend_bff.pb"
	itemlikepb "git.garena.com/shopee/video/core/backend/bff_ecommerce/gen/go/user_activity_item_like.pb"
	"git.garena.com/shopee/video/core/backend/bff_ecommerce/gen/stub"
	"git.garena.com/shopee/video/core/backend/bff_ecommerce/internal/app/server/dto"
	"git.garena.com/shopee/video/core/backend/bff_ecommerce/internal/domain/service"
	"git.garena.com/shopee/video/core/backend/bff_ecommerce/internal/domain/util"
	"git.garena.com/shopee/video/core/backend/bff_ecommerce/internal/infra/facade"
	bffutils "git.garena.com/shopee/video/core/common/bff/utils"
	"git.garena.com/shopee/video/core/common/cid"
	"git.garena.com/shopee/video/core/common/codec"
	"git.garena.com/shopee/video/core/common/errs"
	"git.garena.com/shopee/video/core/common/errutil"
	"git.garena.com/shopee/video/core/common/logutil"
	"github.com/golang/protobuf/proto"
	"github.com/jinzhu/copier"
)

type ProductRPCServiceImpl struct {
	stub.ProductBffRPCStub
	PluginReplacer     *service.PluginReplaceService
	RecommendBffClient facade.RecommendBffClient
	UserActivityClient *facade.UserActivityClient
	CreationClient     *facade.CreationClient
}

func (p *ProductRPCServiceImpl) GetUserItemListBff(ctx context.Context, req *bffecommercepb.GetUserItemListRequest, resp *bffecommercepb.GetUserItemListBffResponse) uint32 {
	resp.Data = new(bffecommercepb.GetUserItemListResponse)
	userId := req.GetBffMeta().GetUserid()
	requestUserId := req.GetRequestUserId()
	if requestUserId == 0 {
		requestUserId = userId
	}
	landingItemId, err := strconv.ParseUint(req.GetLandingItemId(), 10, 64)
	if err != nil {
		logutil.GetThrottleLogger(
			ctx,
			5*time.Minute,
		).WithError(err).WithField("req", req).Info("parse landing item id error")
	}

	deviceInfo := bffutil.BuildDeviceInfo(req.GetBffMeta())

	if req.GetRequestUserId() != 0 && req.GetRequestUserId() != userId {
		userRsp, err := autoclient.DefaultRetrieveClient.GetUserDetails(ctx, &retrievepb.GetUserDetailsRequest{
			UserIds:    []uint64{requestUserId},
			SelfUserId: proto.Uint64(userId),
			Source:     proto.Int32(int32(retrievepb.Constant_DEFAULT_SOURCE)),
		})
		if err != nil {
			return errs.As(err).Code()
		}
		if len(userRsp.GetVideoUserDetails()) == 0 ||
			(userRsp.GetVideoUserDetails()[0].State != nil && userRsp.GetVideoUserDetails()[0].GetState() != int32(userpb.Constant_STATE_NORMAL)) {
			return uint32(bffecommercepb.Constant_ERROR_USER_STATE_ILLEGAL)
		}
	}
	spexReq := &retrievepb.GetUserItemListRequest{
		Uid:         proto.Uint64(requestUserId),
		PageContext: req.PageContext,
		Limit:       req.Limit,
		LoginUserId: proto.Uint64(userId),
		DeviceInfo:  autoconv.DefaultCoreBffecommerce2RetrieveConverter.Convert2DeviceInfo(deviceInfo),
	}
	if landingItemId > 0 {
		spexReq.LandingItemId = proto.Uint64(landingItemId)
	}
	spexResp, err := autoclient.DefaultRetrieveClient.GetUserItemList(ctx, spexReq)
	if err != nil {
		return errs.As(err).Code()
	}
	*resp.Data = *autoconv.DefaultRetrieve2CoreBffecommerceConverter.Convert2GetUserItemListResponse(spexResp)
	return errutil.Success
}

func (p *ProductRPCServiceImpl) GetPostProductsBff(ctx context.Context, req *bffecommercepb.GetPostProductsRequest, resp *bffecommercepb.GetPostProductsBffResponse) uint32 {
	resp.Data = new(bffecommercepb.GetPostProductsResponse)
	ss := bffutil.NewMetaSearch(req.GetBffMeta())
	bffutils.BuildAppHeader(ctx, ss, bffutils.BuildKeyRequestInfo, bffutils.BuildKeyGeoIPCountryCode)
	req.DeviceInfo = p.PluginReplacer.ReplaceDeviceInfoFromSearcher(ctx, ss, req.DeviceInfo)

	postId, postUid, err := codec.DecodePostID(req.GetPostId())
	if err != nil {
		logutil.GetLogger(ctx).WithField("req", req).Error("decode post id error")
		return errutil.ErrParam
	}

	spexReq := &retrievepb.GetPostProductsRequest{
		PostIdent: &retrievepb.PostIdent{
			PostId: proto.Uint64(postId),
			UserId: proto.Uint64(postUid),
		},
		Uid:           proto.Uint64(req.GetBffMeta().GetUserid()),
		SpecificItems: autoconv.DefaultCoreBffecommerce2RetrieveConverter.ConvertGetPostProductsRequest_ShopItemID2ShopItemIDs(req.GetSpecificItems()),
		Pagination: &retrievepb.GetPostProductsRequest_Pagination{
			Context: proto.String(req.GetPagination().GetContext()),
			Limit:   proto.Uint32(req.GetPagination().GetLimit()),
		},
		NeedYmalItem:       proto.Bool(req.GetQueryContext().GetNeedYmalItemList()),
		NeedItemCardV2:     proto.Bool(req.GetQueryContext().GetNeedProductV2()),
		ProductV2Scene:     proto.String(req.GetQueryContext().GetProductV2Scene()),
		DeviceInfo:         autoconv.DefaultCoreBffecommerce2RetrieveConverter.Convert2DeviceInfo(req.GetDeviceInfo()),
		BffMeta:            autoconv.DefaultCoreBffecommerce2RetrieveConverter.Convert2BffRequestMeta(req.GetBffMeta()),
		UgParam:            proto.String(req.GetUgParam()),
		ShowItemCustomName: proto.Bool(true),
		ExtInfo:            autoconv.DefaultCoreBffecommerce2RetrieveConverter.Convert2ExtInfos(req.GetExtInfo()),
		FilterCev:          req.FilterCev,
		FilterSev:          req.FilterSev,
	}
	if req.GetPreferPromotion().GetItemId() != 0 || req.GetPreferPromotion().GetPromotionId() != 0 {
		spexReq.PreferPostPromotion = &retrievepb.PreferPostPromotion{
			ItemId:      proto.Uint64(req.GetPreferPromotion().GetItemId()),
			PromotionId: proto.Uint64(req.GetPreferPromotion().GetPromotionId()),
			PostId:      proto.Uint64(postId),
		}
	}

	spexResp, err := autoclient.DefaultRetrieveClient.GetPostProducts(ctx, spexReq)
	if err != nil {
		return errs.As(err).Code()
	}

	*resp.Data = dto.ProductsDTO(spexResp)
	return errutil.Success
}

func (p *ProductRPCServiceImpl) GetItemsWithImportUrlBff(ctx context.Context, req *bffecommercepb.GetItemsWithImportUrlRequest,
	rsp *bffecommercepb.GetItemsWithImportUrlBffResponse) uint32 {
	rsp.Data = new(bffecommercepb.GetItemsWithImportUrlResponse)
	spexReq := autoconv.DefaultCoreBffecommerce2RetrieveConverter.Convert2GetItemsWithImportUrlRequest(req)
	deviceInfo := bffutil.BuildDeviceInfo(req.GetBffMeta())
	spexReq.DeviceInfo = autoconv.DefaultCoreBffecommerce2RetrieveConverter.Convert2DeviceInfo(deviceInfo)
	spexRsp, err := autoclient.DefaultRetrieveClient.GetItemsWithImportUrl(ctx, spexReq)
	if err != nil {
		return errs.As(err).Code()
	}
	*rsp.Data = *autoconv.DefaultRetrieve2CoreBffecommerceConverter.Convert2GetItemsWithImportUrlResponse(spexRsp)
	return 0
}

func (p *ProductRPCServiceImpl) GetItemsWithAffiliateBff(ctx context.Context, req *bffecommercepb.GetItemsWithAffiliateRequest,
	rsp *bffecommercepb.GetItemsWithAffiliateBffResponse) uint32 {
	rsp.Data = new(bffecommercepb.GetItemsWithAffiliateResponse)
	deviceInfo := bffutil.BuildDeviceInfo(req.GetBffMeta())

	spexReq := autoconv.DefaultCoreBffecommerce2RetrieveConverter.Convert2GetItemsWithAffiliateRequest(req)
	spexReq.DeviceInfo = autoconv.DefaultCoreBffecommerce2RetrieveConverter.Convert2DeviceInfo(deviceInfo)

	spexRsp, err := autoclient.DefaultRetrieveClient.GetItemsWithAffiliate(ctx, spexReq)
	if err != nil {
		return errs.As(err).Code()
	}
	*rsp.Data = *autoconv.DefaultRetrieve2CoreBffecommerceConverter.Convert2GetItemsWithAffiliateResponse(spexRsp)
	return 0
}

func (p *ProductRPCServiceImpl) GetItemDetailBff(ctx context.Context, req *bffecommercepb.GetItemDetailRequest,
	rsp *bffecommercepb.GetItemDetailBffResponse) uint32 {
	rsp.Data = new(bffecommercepb.GetItemDetailResponse)
	deviceInfo := bffutil.BuildDeviceInfo(req.GetBffMeta())
	spexReq := autoconv.DefaultCoreBffecommerce2RetrieveConverter.Convert2GetItemDetailRequest(req)
	spexReq.DeviceInfo = autoconv.DefaultCoreBffecommerce2RetrieveConverter.Convert2DeviceInfo(deviceInfo)

	spexRsp, err := autoclient.DefaultRetrieveClient.GetItemDetail(ctx, spexReq)
	if err != nil {
		return errs.As(err).Code()
	}
	*rsp.Data = *autoconv.DefaultRetrieve2CoreBffecommerceConverter.Convert2GetItemDetailResponse(spexRsp)
	return 0
}

func (p *ProductRPCServiceImpl) GetVskuDetailBff(ctx context.Context, req *bffecommercepb.GetVskuDetailRequest,
	rsp *bffecommercepb.GetVskuDetailBffResponse) uint32 {
	rsp.Data = new(bffecommercepb.GetVskuDetailResponse)
	deviceInfo := bffutil.BuildDeviceInfo(req.GetBffMeta())
	spexReq := autoconv.DefaultCoreBffecommerce2RetrieveConverter.Convert2GetVskuDetailRequest(req)
	spexReq.DeviceInfo = autoconv.DefaultCoreBffecommerce2RetrieveConverter.Convert2DeviceInfo(deviceInfo)

	spexRsp, err := autoclient.DefaultRetrieveClient.GetVskuDetail(ctx, spexReq)
	if err != nil {
		return errs.As(err).Code()
	}
	*rsp.Data = *autoconv.DefaultRetrieve2CoreBffecommerceConverter.Convert2GetVskuDetailResponse(spexRsp)
	return 0
}

func (p *ProductRPCServiceImpl) GetItemAnimationBubble(ctx context.Context, req *bffecommercepb.GetItemAnimationBubbleRequest,
	rsp *bffecommercepb.GetItemAnimationBubbleResponse) uint32 {
	spexRsp, err := autoclient.DefaultRetrieveClient.GetItemAnimationBubble(ctx, &retrievepb.GetItemAnimationBubbleRequest{
		ShopItemId: &retrievepb.ShopItemID{
			ShopId: proto.Int64(req.GetShopId()),
			ItemId: proto.Uint64(req.GetItemId()),
		},
	})
	if err != nil {
		return errs.As(err).Code()
	}

	rsp.Data = autoconv.DefaultRetrieve2CoreBffecommerceConverter.ConvertGetItemAnimationBubbleResponse2GetItemAnimationBubbleResponse_Data(spexRsp)
	return 0
}

func (p *ProductRPCServiceImpl) GetItemLiveSession(ctx context.Context, req *bffecommercepb.GetItemLiveSessionRequest,
	rsp *bffecommercepb.GetItemLiveSessionResponse) (code uint32) {
	defer func() {
		rsp.Code = proto.Uint32(code)
		rsp.Msg = proto.String("")
	}()

	if req.GetShopUserId() == 0 || req.GetItemId() == 0 {
		logutil.GetLogger(ctx).
			WithField("shop_user_id", req.GetShopUserId()).
			WithField("item_id", req.GetItemId()).Warn("get_item_live_session: zero param")
		return uint32(commonpb.Constant_ERROR_PARAMS)
	}

	resp, err := facade.DefaultLiveStreamingClient.GetMiniFeedLiveInfo(ctx, &live_gateway.GetMiniFeedLiveInfoRequest{
		StreamerId: proto.Uint64(req.GetShopUserId()),
		ItemId:     proto.Uint64(req.GetItemId()),
		ViewerId:   proto.Uint64(req.GetBffMeta().GetUserid()),
		DeviceId:   proto.String(req.GetBffMeta().GetDeviceid()),
	})

	if err != nil {
		code = errs.As(err).Code()
		return code
	}

	rsp.Data = &bffecommercepb.GetItemLiveSessionData{}
	if sess := resp.GetSession(); sess != nil {
		rsp.Data.Session = &bffecommercepb.ItemLiveSession{
			SessionId:          proto.Uint64(sess.GetSessionId()),
			PlayUrl:            proto.String(sess.GetPlayUrl()),
			Cover:              proto.String(sess.GetCover()),
			RecommendationInfo: proto.String(sess.GetRecommendationInfo()),
		}
	}
	return 0
}

func (p *ProductRPCServiceImpl) GetSimilarItem(ctx context.Context, req *bffecommercepb.GetSimilarItemRequest, rsp *bffecommercepb.GetSimilarItemResponse) (code uint32) {
	defer func() {
		rsp.Code = proto.Uint32(code)
		rsp.Msg = proto.String("")
	}()

	if req.GetItemId() == 0 || req.GetShopId() == 0 || req.GetModel() == nil {
		logutil.GetLogger(ctx).WithField("req", req).Warn("[similar item] invalid params")
		return uint32(commonpb.Constant_ERROR_PARAMS)
	}

	meta := &recommendbffpb.RequestMeta{}
	err := copier.Copy(meta, req.GetBffMeta())
	if err != nil {
		logutil.GetLogger(ctx).WithError(err).WithField("src", req.GetBffMeta()).Error("[similar item] copy bff_meta failed")
	}

	var labelIDs = req.GetLabelIds()
	if len(req.GetLabelIds()) == 0 {
		listingLabelID, _ := facade.DefaultListingClient.GetItemLabelList(ctx, []uint64{req.GetItemId()})
		itemLabelID := listingLabelID[req.GetItemId()]
		for _, v := range itemLabelID {
			labelIDs = append(labelIDs, int64(v))
		}
	}

	model := req.GetModel()
	sprItemInfo := &recommendbffpb.SPRItemInfo{
		Shopid:             proto.Int64(int64(req.GetShopId())),
		Itemid:             proto.Int64(int64(req.GetItemId())),
		BeforeVoucherPrice: proto.Int64(model.GetBeforeVoucherPrice()),
		AfterVoucherPrice:  proto.Int64(model.GetAfterVoucherPrice()),
		Modelid:            proto.Int64(model.GetModelid()),
	}

	rcmdReq := &recommendbffpb.SimilarProductRcmdBannerRequest{
		BffMeta:    meta,
		Shopid:     proto.Int64(int64(req.GetShopId())),
		Itemid:     proto.Int64(int64(req.GetItemId())),
		IsBanner:   proto.Bool(true),
		LabelIds:   labelIDs,
		FromSource: proto.String(req.GetFromSource()),
		ModelList: []*recommendbffpb.SPRItemInfo{
			sprItemInfo,
		},
		BeforeVoucherPrice: proto.Int64(model.GetBeforeVoucherPrice()),
		AfterVoucherPrice:  proto.Int64(model.GetAfterVoucherPrice()),
	}

	rcmdRsp, err := p.RecommendBffClient.SimilarProductRcmdBanner(ctx, rcmdReq)
	if err != nil {
		logutil.GetLogger(ctx).WithField("req", req).WithError(err).Error("[similar item] downstream err.")
		return errs.As(err).Code()
	}

	rsp.Data = &bffecommercepb.GetSimilarItemData{}

	var cheapest *recommendbffpb.ItemCardItemFull
	for _, it := range rcmdRsp.GetData() {
		if cheapest == nil {
			cheapest = it
			continue
		}

		if it.GetItemCardDisplayPrice().GetPrice() < cheapest.GetItemCardDisplayPrice().GetPrice() {
			cheapest = it
		}
	}

	if cheapest != nil {
		rsp.Data.ItemId = proto.Int64(int64(req.GetItemId()))
		rsp.Data.ShopId = proto.Int64(int64(req.GetShopId()))
		// 透传给rcmd FE的，暂时填充为FE的model
		// 后续rcmd侧会自己去fetch model，然后返回给我们
		rsp.Data.ModelList = []*bffecommercepb.SPRItemInfo{
			model,
		}

		rsp.Data.Cheapest = &bffecommercepb.SimilarItem{
			ItemId: proto.Int64(cheapest.GetItemid()),
			ShopId: proto.Int64(cheapest.GetShopid()),
		}

		finalPrice := cheapest.GetItemCardDisplayPrice()
		rsp.Data.Cheapest.Price = proto.Int64(finalPrice.GetPrice())
		rsp.Data.Cheapest.ModelId = proto.Int64(int64(finalPrice.GetModelId()))

		if finalPrice.GetPrice() != rcmdRsp.GetCheapestPrice() {
			logutil.GetLogger(ctx).WithField("req", req).WithError(err).Error("[similar item] item price is not cheapest price")
		}
	}

	return 0
}

func (p *ProductRPCServiceImpl) GetPostRecommendReplaceItems(ctx context.Context, req *bffecommercepb.GetPostRCMDReplaceItemsRequest, rsp *bffecommercepb.GetPostRCMDReplaceItemsResponse) uint32 {
	rsp.Data = new(bffecommercepb.GetPostRCMDReplaceItemsData)
	deviceInfo := bffutil.BuildDeviceInfo(req.GetBffMeta())

	postId, postUid, err := codec.DecodePostID(req.GetPostId())
	if err != nil {
		logutil.GetLogger(ctx).WithField("req", req).Error("decode post id error")
		return errutil.ErrParam
	}

	if postUid != req.GetBffMeta().GetUserid() {
		logutil.GetLogger(ctx).WithField("req", req).Error("only can view self post")
		return errutil.ErrParam
	}

	spexReq := autoconv.DefaultCoreBffecommerce2RetrieveConverter.Convert2GetPostRCMDReplaceItemsRequest(req)
	spexReq.DeviceInfo = autoconv.DefaultCoreBffecommerce2RetrieveConverter.Convert2DeviceInfo(deviceInfo)
	spexReq.PostIdent = &retrievepb.PostIdent{
		PostId: proto.Uint64(postId),
		UserId: proto.Uint64(postUid),
	}

	spexRsp, err := autoclient.DefaultRetrieveClient.GetPostRecommendReplaceItems(ctx, spexReq)
	if err != nil {
		return errs.As(err).Code()
	}

	*rsp.Data = *autoconv.DefaultRetrieve2CoreBffecommerceConverter.ConvertGetPostRCMDReplaceItemsResponse2GetPostRCMDReplaceItemsData(spexRsp)
	return 0
}

func (p *ProductRPCServiceImpl) GetPostReplacedItems(ctx context.Context, req *bffecommercepb.GetPostReplacedItemsRequest, rsp *bffecommercepb.GetPostReplacedItemsResponse) uint32 {
	rsp.Data = new(bffecommercepb.GetPostReplacedItemListData)
	deviceInfo := bffutil.BuildDeviceInfo(req.GetBffMeta())

	postId, postUid, err := codec.DecodePostID(req.GetPostId())
	if err != nil {
		logutil.GetLogger(ctx).WithField("req", req).Error("decode post id error")
		return errutil.ErrParam
	}

	if postUid != req.GetBffMeta().GetUserid() {
		logutil.GetLogger(ctx).WithField("req", req).Error("only can view self post")
		return errutil.ErrParam
	}

	spexReq := autoconv.DefaultCoreBffecommerce2RetrieveConverter.Convert2GetPostReplacedItemsRequest(req)
	spexReq.DeviceInfo = autoconv.DefaultCoreBffecommerce2RetrieveConverter.Convert2DeviceInfo(deviceInfo)
	spexReq.PostIdent = &retrievepb.PostIdent{
		PostId: proto.Uint64(postId),
		UserId: proto.Uint64(postUid),
	}

	spexRsp, err := autoclient.DefaultRetrieveClient.GetPostReplacedItems(ctx, spexReq)
	if err != nil {
		return errs.As(err).Code()
	}

	*rsp.Data = *autoconv.DefaultRetrieve2CoreBffecommerceConverter.ConvertGetPostReplacedItemsResponse2GetPostReplacedItemListData(spexRsp)
	return 0
}

func (p *ProductRPCServiceImpl) LikeItem(ctx context.Context, req *bffecommercepb.LikeItemRequest, rsp *bffecommercepb.LikeItemResponse) uint32 {
	if req.GetBffMeta().GetUserid() == 0 {
		logutil.GetLogger(ctx).WithField("req", req).Warn("[like_item] need auth")
		return uint32(commonpb.Constant_ERROR_NEED_AUTH)
	}
	if req.Liked == nil || req.GetItemId() == 0 || req.GetShopId() == 0 {
		logutil.GetLogger(ctx).WithField("req", req).Info("[like_item] invalid params")
		return uint32(commonpb.Constant_ERROR_PARAMS)
	}

	err := p.UserActivityClient.SetUserItemLike(ctx, &itemlikepb.SetUserItemLikeRequest{
		UserId: proto.Uint64(req.GetBffMeta().GetUserid()),
		ShopId: proto.Uint64(req.GetShopId()),
		ItemId: proto.Uint64(req.GetItemId()),
		Liked:  proto.Bool(req.GetLiked()),
		Region: proto.String(cid.GetCID(ctx)),
	})

	if err != nil {
		return errs.As(err).Code()
	}
	return errutil.Success
}

func (p *ProductRPCServiceImpl) GetItemFittingInfo(ctx context.Context, req *bffecommercepb.GetItemFittingInfoRequest, rsp *bffecommercepb.GetItemFittingInfoResponse) uint32 {
	if req.GetItemId() == 0 || req.GetShopId() == 0 {
		logutil.GetLogger(ctx).WithField("shop_id", req.GetShopId()).WithField("item_id", req.GetItemId()).Info("[fitting] invalid params")
		return uint32(commonpb.Constant_ERROR_PARAMS)
	}
	rs, err := p.CreationClient.GetFittingRoomInfo(ctx, &creationpb.GetItemFittingInfoRequest{
		ShopId: req.ShopId,
		ItemId: req.ItemId,
	})
	if err != nil {
		logutil.GetLogger(ctx).WithField("req", req).WithError(err).Error("[fitting] get room info failed")
		return errs.As(err).Code()
	}

	cards := make([]*bffecommercepb.DisplayItemCard, 0, len(rs.GetDisplayItemCards()))
	for _, it := range rs.GetDisplayItemCards() {
		cards = append(cards, &bffecommercepb.DisplayItemCard{
			ShopId:       it.ShopId,
			ItemId:       it.ItemId,
			ClothingType: it.ClothingType,
			DisplayImage: it.DisplayImage,
		})
	}

	rsp.Data = &bffecommercepb.GetItemFittingInfoData{
		HasFittingInfo:   proto.Bool(rs.GetHasFittingInfo()),
		DisplayItemCards: cards,
	}
	return errutil.Success
}

func (p *ProductRPCServiceImpl) GetYmalItems(ctx context.Context, req *bffecommercepb.GetYmalItemsRequest, rsp *bffecommercepb.GetYmalItemsResponse) uint32 {
	if req.GetItemId() == 0 {
		logutil.GetLogger(ctx).WithField("item_id", req.GetItemId()).Info("invalid params")
		return uint32(commonpb.Constant_ERROR_PARAMS)
	}

	// 临时fix: SPV-39716
	util.FixClientPlatform(ctx, req.GetBffMeta())

	spexReq := autoconv.DefaultCoreBffecommerce2RetrieveConverter.Convert2GetYmalItemsRequest(req)

	spexRsp, err := autoclient.DefaultRetrieveClient.GetYmalItems(ctx, spexReq)
	if err != nil {
		return errs.As(err).Code()
	}

	rsp.Data = &bffecommercepb.GetYmalItemsData{
		Items: autoconv.DefaultRetrieve2CoreBffecommerceConverter.Convert2YmalItems(spexRsp.GetItems()),
	}

	return errutil.Success
}

func (p *ProductRPCServiceImpl) SelectVariation(ctx context.Context, req *bffecommercepb.SelectVariationRequest, rsp *bffecommercepb.SelectVariationResponse) uint32 {
	ss := bffutil.NewMetaSearch(req.GetBffMeta())
	bffutils.BuildAppHeader(ctx, ss, bffutils.BuildKeyClientIp, bffutils.BuildKeyRequestInfo, bffutils.BuildKeyGeoIPCountryCode)
	deviceInfo := bffutil.BuildDeviceInfoFromSearcher(ss)
	deviceInfo.DeviceFingerprint = req.GetBffMeta().DeviceFingerprint

	spexReq := &retrievepb.SelectVariationRequest{
		ShopId:         req.ShopId,
		ItemId:         req.ItemId,
		SelectModelId:  req.SelectModelId,
		ProductV2Scene: req.ProductV2Scene,
		UserId:         req.GetBffMeta().Userid,
		DeviceInfo:     autoconv.DefaultCoreBffecommerce2RetrieveConverter.Convert2DeviceInfo(deviceInfo),
	}
	postId, userId, err := codec.DecodePostID(req.GetFromPostId())
	if err != nil {
		logutil.GetLogger(ctx).WithField("post_id", req.GetFromPostId()).Info("invalid post id")
	}
	if postId != 0 && userId != 0 {
		spexReq.FromPostId = &retrievepb.PostIdent{
			PostId: proto.Uint64(postId),
			UserId: proto.Uint64(userId),
		}
	}

	spexRsp, err := autoclient.DefaultRetrieveClient.SelectVariation(ctx, spexReq)
	if err != nil {
		return errs.As(err).Code()
	}
	rsp.Data = &bffecommercepb.SelectVariationData{}
	if spexRsp.GetProductV2() != nil {
		rsp.Data.ProductV2 = autoconv.DefaultRetrieve2CoreBffecommerceConverter.Convert2ItemCardDisplayedDO(spexRsp.GetProductV2())
	}
	return errutil.Success
}
