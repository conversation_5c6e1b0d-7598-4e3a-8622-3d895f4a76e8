package server

import (
	"context"
	"strings"

	"git.garena.com/shopee/platform/splog"
	"git.garena.com/shopee/video/core/backend/bff_ecommerce/gen/autoclient"
	"git.garena.com/shopee/video/core/backend/bff_ecommerce/gen/autoconv"
	bizpb "git.garena.com/shopee/video/core/backend/bff_ecommerce/gen/go/luckyvideo_biz.pb"
	bffecommercepb "git.garena.com/shopee/video/core/backend/bff_ecommerce/gen/go/luckyvideo_core_bff_ecommerce.pb"
	retrievepb "git.garena.com/shopee/video/core/backend/bff_ecommerce/gen/go/luckyvideo_retrieve.pb"
	userpb "git.garena.com/shopee/video/core/backend/bff_ecommerce/gen/go/luckyvideo_user.pb"
	"git.garena.com/shopee/video/core/backend/bff_ecommerce/gen/stub"
	"git.garena.com/shopee/video/core/backend/bff_ecommerce/internal/app/server/dto"
	"git.garena.com/shopee/video/core/backend/bff_ecommerce/internal/domain/service"
	"git.garena.com/shopee/video/core/common/codec"
	"git.garena.com/shopee/video/core/common/errs"
	"git.garena.com/shopee/video/core/common/errutil"
	"git.garena.com/shopee/video/core/common/logutil"
	reporter "git.garena.com/shopee/video/core/common/reporter"
	"github.com/golang/protobuf/proto"
)

type VoucherRPCServiceImpl struct {
	stub.VoucherBffRPCStub
	AbTestService *service.AbTestService
}

func (v *VoucherRPCServiceImpl) GetPostClaimedVoucherBff(ctx context.Context, req *bffecommercepb.GetPostClaimedVoucherRequest, resp *bffecommercepb.GetPostClaimedVoucherBffResponse) uint32 {
	resp.Data = new(bffecommercepb.GetPostClaimedVoucherResponse)
	postId, postUid, err := codec.DecodePostID(req.GetPostId())
	if err != nil {
		logutil.GetLogger(ctx).Error("decode post id error")
		return errutil.ErrParam
	}
	spexReq := &retrievepb.GetPostClaimedVoucherRequest{
		PostIdent: &retrievepb.PostIdent{
			PostId: proto.Uint64(postId),
			UserId: proto.Uint64(postUid),
		},
		SpecificItems: autoconv.DefaultCoreBffecommerce2RetrieveConverter.ConvertGetPostClaimedVoucherRequest_ShopItemID2ShopItemIDs(req.GetSpecificItems()),
		FetchCount:    req.FetchCount,
		BffMeta: &retrievepb.BffRequestMeta{
			Userid: proto.Uint64(req.GetBffMeta().GetUserid()),
		},
	}
	spexResp, err := autoclient.DefaultRetrieveClient.GetPostClaimedVoucher(ctx, spexReq)
	if err != nil {
		return errs.As(err).Code()
	}

	*resp.Data = *autoconv.DefaultRetrieve2CoreBffecommerceConverter.Convert2GetPostClaimedVoucherResponse(spexResp)
	return errutil.Success
}

func (v *VoucherRPCServiceImpl) GetSellerVoucherListBff(ctx context.Context, req *bffecommercepb.GetSellerVoucherListRequest, resp *bffecommercepb.GetSellerVoucherListBffResponse) uint32 {
	resp.Data = new(bffecommercepb.GetSellerVoucherListResponse)
	spexReq := &retrievepb.GetSellerVoucherListRequest{
		PromotionId: proto.Int64(0),
		ShopId:      proto.Int64(int64(req.GetShopId())),
		ItemIds:     req.GetItemIdList(),
	}
	spexResp, err := autoclient.DefaultRetrieveClient.GetSellerVoucherList(ctx, spexReq)
	if err != nil {
		return errs.As(err).Code()
	}
	*resp.Data = dto.SellerVoucherListDTO(req.GetShopId(), req.GetItemIdList(), spexResp)
	return errutil.Success
}

func (v *VoucherRPCServiceImpl) GetUserVoucherListBff(ctx context.Context, req *bffecommercepb.GetUserVoucherListRequest, resp *bffecommercepb.GetUserVoucherListBffResponse) uint32 {
	resp.Data = new(bffecommercepb.GetUserVoucherListResponse)
	shopID := uint64(0)
	userID := req.GetBffMeta().GetUserid()
	userDetails, err := autoclient.DefaultUserClient.GetUserDetails(ctx, &userpb.GetUserDetailsRequest{
		UserIds:    []uint64{userID},
		SelfUserId: proto.Uint64(userID),
	})
	// ignore error, set shopID only when userDetails is not nil
	if err == nil && userDetails != nil && len(userDetails.GetVideoUserDetails()) > 0 {
		shopID = userDetails.GetVideoUserDetails()[0].GetShopId()
	}
	// metric: deprecate_sts_shopid_result
	// label:
	//  - l0: cmd
	//  - l1: bool(shopID > 0, should be true almost all the time)
	reporter.CounterOnceContext(ctx, "deprecate_sts_shopid_result", "get_user_voucher_list_bff", shopID > 0)
	spexReq := &retrievepb.GetVoucherListRequest{
		ShopId:  proto.Int64(int64(shopID)),
		ItemIds: req.GetItemIdList(),
		UserId:  proto.Uint64(req.GetBffMeta().GetUserid()),
		NeedTop: proto.Bool(req.GetNeedTop()),
	}
	spexResp, err := autoclient.DefaultRetrieveClient.GetUserVoucherList(ctx, spexReq)
	if err != nil {
		return errs.As(err).Code()
	}
	*resp.Data = dto.UserVoucherListDTO(shopID, req.GetItemIdList(), spexResp)
	return errutil.Success
}

func (v *VoucherRPCServiceImpl) GenerateVoucherTokenBff(ctx context.Context, req *bffecommercepb.GenerateVoucherTokenRequest, resp *bffecommercepb.GenerateVoucherTokenBffResponse) uint32 {
	resp.Data = new(bffecommercepb.GenerateVoucherTokenResponse)
	logger := logutil.GetLogger(ctx).WithFields(splog.Fields{
		"postId":     req.GetPostId(),
		"profileUid": req.GetProfileUid(),
		"itemId":     req.GetItemId(),
	})
	if len(req.GetPostId()) == 0 && req.GetProfileUid() == 0 {
		logger.Error("post_id or profile_uid is required")
		return errutil.ErrParam
	}
	var (
		postId  uint64
		postUid uint64
		err     error
	)

	if len(req.GetPostId()) > 0 {
		postId, postUid, err = codec.DecodePostID(req.GetPostId())
		if err != nil {
			logger.Error("decode post id error")
			return errutil.ErrParam
		}
	}

	var svReferer string
	for _, header := range req.GetBffMeta().GetHeaders() {
		if strings.EqualFold("Sv-Referer", header.GetName()) {
			svReferer = header.GetValue()
			break
		}
	}
	if strings.EqualFold("pdp_floating_window_landing_page", svReferer) {
		if v.AbTestService.FilterExclusiveVoucher(ctx, req.GetBffMeta().GetUserid()) {
			logger.Info("skip generate voucher token")
			return uint32(bffecommercepb.Constant_ERROR_NOT_SUPPORT_EXCLUSIVE_VOUCHER)
		}
	}

	logger.WithField("Sv-Referer", svReferer).Debug("pre generate voucher token")
	spexReq := &bizpb.GenerateVoucherTokenRequest{
		Payload: &bizpb.VoucherTokenPayload{
			Uid:            proto.Uint64(req.GetBffMeta().GetUserid()),
			VideoId:        proto.Uint64(postId),
			VideoCreatorId: proto.Uint64(postUid),
			ItemId:         req.ItemId,
			ProfileUid:     req.ProfileUid,
		},
	}
	spexResp, err := autoclient.DefaultBizClient.GenerateVoucherToken(ctx, spexReq)
	if err != nil {
		return errs.As(err).Code()
	}
	resp.Data.Token = spexResp.Token
	return errutil.Success
}
